// controllers/homeController.js

const renderHomePage = (req, res) => {
  try {
    res.render('pages/home', {
      pageTitle: 'Projeto PCR-Labor',
      currentPage: 'home'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao carregar página inicial'
    });
  }
};

const renderAboutPage = (req, res) => {
  try {
    res.render('pages/about', {
      pageTitle: 'Sobre - PCR-Labor',
      currentPage: 'about'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao carregar página sobre'
    });
  }
};

const renderContactPage = (req, res) => {
  try {
    res.render('pages/contact', {
      pageTitle: 'Contato - PCR-Labor',
      currentPage: 'contact'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao carregar página de contato'
    });
  }
};

module.exports = {
  renderHomePage,
  renderAboutPage,
  renderContactPage
};
