// controllers/userController.js

const Usuario = require('../models/userModel');

const getAllUsers = async (req, res) => {
  try {
    const users = await Usuario.getAll();
    res.status(200).json({
      success: true,
      data: users,
      message: 'Usuários recuperados com sucesso'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const getUserById = async (req, res) => {
  try {
    const user = await Usuario.getById(req.params.id);
    if (user) {
      res.status(200).json({
        success: true,
        data: user,
        message: 'Usuário encontrado'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const createUser = async (req, res) => {
  try {
    const { nome, email, senha, id_empresa } = req.body;
    const newUser = await Usuario.create({ nome, email, senha, id_empresa });
    res.status(201).json({
      success: true,
      data: newUser,
      message: 'Usuário criado com sucesso'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const updateUser = async (req, res) => {
  try {
    const { nome, email, id_empresa } = req.body;
    const updatedUser = await Usuario.update(req.params.id, { nome, email, id_empresa });
    if (updatedUser) {
      res.status(200).json({
        success: true,
        data: updatedUser,
        message: 'Usuário atualizado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const deleteUser = async (req, res) => {
  try {
    const deleted = await Usuario.delete(req.params.id);
    if (deleted) {
      res.status(200).json({
        success: true,
        message: 'Usuário deletado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
};
