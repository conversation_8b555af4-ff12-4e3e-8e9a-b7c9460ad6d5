### Páginas Web
GET http://localhost:3000/

###

### Página Sobre
GET http://localhost:3000/sobre

###

### Página Contato
GET http://localhost:3000/contato

###

### API - Buscar todos os usuários
GET http://localhost:3000/api/users
Accept: application/json

###

### API - Buscar usuário por ID
GET http://localhost:3000/api/users/1
Accept: application/json

###

### API - Criar novo usuário
POST http://localhost:3000/api/users
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>"
}

###

### API - Atualizar usuário
PUT http://localhost:3000/api/users/1
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>"
}

###

### API - Deletar usuário
DELETE http://localhost:3000/api/users/1
Accept: application/json
