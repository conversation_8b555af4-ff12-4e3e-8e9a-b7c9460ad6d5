<!-- LATERAL DIREITA (SEMPRE PRESENTE) -->
<aside class="sidebar-right">
  <!-- Tarefas: Lista de tarefa<PERSON> r<PERSON> (DESIGN MELHORADO) -->
  <div class="tasks-section">
    <h3 class="section-title">
      <span>✅</span>
      Tarefas
    </h3>

    <!-- Caixa das tarefas com design melhorado -->
    <div style="background: white; border: 1px solid #e0e0e0; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
        <span style="font-size: 14px; font-weight: 600; color: #333;">Pendentes</span>
        <span style="background: #f59e0b; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 600;">4</span>
      </div>

      <div style="display: grid; gap: 12px;">
        <div style="display: flex; align-items: center; gap: 12px; padding: 10px; background: #f8f9fa; border-radius: 8px; transition: all 0.2s ease;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='#f8f9fa'">
          <input type="checkbox" id="task1" style="margin: 0; cursor: pointer;" onchange="toggleTask(this)">
          <label for="task1" style="flex: 1; font-size: 13px; color: #333; cursor: pointer; margin: 0;">Revisar pedidos pendentes</label>
          <span style="color: #f59e0b; font-size: 11px;">🔥 Urgente</span>
        </div>

        <div style="display: flex; align-items: center; gap: 12px; padding: 10px; background: #dcfce7; border-radius: 8px; opacity: 0.7;">
          <input type="checkbox" id="task2" checked style="margin: 0; cursor: pointer;" onchange="toggleTask(this)">
          <label for="task2" style="flex: 1; font-size: 13px; color: #333; cursor: pointer; margin: 0; text-decoration: line-through;">Sincronizar Mercado Livre</label>
          <span style="color: #16a34a; font-size: 11px;">✅ Feito</span>
        </div>

        <div style="display: flex; align-items: center; gap: 12px; padding: 10px; background: #f8f9fa; border-radius: 8px; transition: all 0.2s ease;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='#f8f9fa'">
          <input type="checkbox" id="task3" style="margin: 0; cursor: pointer;" onchange="toggleTask(this)">
          <label for="task3" style="flex: 1; font-size: 13px; color: #333; cursor: pointer; margin: 0;">Atualizar preços Shopee</label>
          <span style="color: #3b82f6; font-size: 11px;">📅 Hoje</span>
        </div>

        <div style="display: flex; align-items: center; gap: 12px; padding: 10px; background: #f8f9fa; border-radius: 8px; transition: all 0.2s ease;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='#f8f9fa'">
          <input type="checkbox" id="task4" style="margin: 0; cursor: pointer;" onchange="toggleTask(this)">
          <label for="task4" style="flex: 1; font-size: 13px; color: #333; cursor: pointer; margin: 0;">Verificar estoque baixo</label>
          <span style="color: #f59e0b; font-size: 11px;">⚠️ Alerta</span>
        </div>

        <div style="display: flex; align-items: center; gap: 12px; padding: 10px; background: #f8f9fa; border-radius: 8px; transition: all 0.2s ease;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='#f8f9fa'">
          <input type="checkbox" id="task5" style="margin: 0; cursor: pointer;" onchange="toggleTask(this)">
          <label for="task5" style="flex: 1; font-size: 13px; color: #333; cursor: pointer; margin: 0;">Enviar relatório mensal</label>
          <span style="color: #8b5cf6; font-size: 11px;">📊 Relatório</span>
        </div>
      </div>

      <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
        <button onclick="adicionarTarefa()" style="width: 100%; padding: 8px; background: #f8f9fa; border: 1px dashed #ccc; border-radius: 6px; color: #666; font-size: 12px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='#f8f9fa'">
          + Adicionar tarefa
        </button>
      </div>
    </div>
  </div>

  <!-- Calendário: Pequeno calendário mensal -->
  <div class="calendar-section">
    <h3 class="section-title">
      <span>📅</span>
      Calendário
    </h3>

    <div class="mini-calendar">
      <div class="calendar-header" id="currentMonth">
        Janeiro 2024
      </div>

      <div class="calendar-grid" id="calendarGrid">
        <!-- Cabeçalho dos dias -->
        <div style="font-weight: bold; color: #666; text-align: center;">D</div>
        <div style="font-weight: bold; color: #666; text-align: center;">S</div>
        <div style="font-weight: bold; color: #666; text-align: center;">T</div>
        <div style="font-weight: bold; color: #666; text-align: center;">Q</div>
        <div style="font-weight: bold; color: #666; text-align: center;">Q</div>
        <div style="font-weight: bold; color: #666; text-align: center;">S</div>
        <div style="font-weight: bold; color: #666; text-align: center;">S</div>

        <!-- Dias do mês serão inseridos aqui via JavaScript -->
      </div>
    </div>
  </div>

  <!-- Botão "Pergunte para IA" -->
  <div class="ai-section">
    <h3 class="section-title">
      <span>🤖</span>
      Assistente IA
    </h3>

    <button class="ai-button" onclick="perguntarIA()">
      Pergunte para IA
    </button>

    <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666;">
      <strong>💡 Dica do dia:</strong><br>
      <span id="aiTip">Considere reabastecer o estoque de Kit PCR COVID-19. Vendas aumentaram 15% esta semana.</span>
    </div>
  </div>
</aside>

<script>
  // Gerar calendário (CORRIGIDO)
  function generateCalendar() {
    const now = new Date();
    const currentMonth = now.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    document.getElementById('currentMonth').textContent = currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1);

    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Calcular o primeiro dia da semana a ser mostrado
    const startDate = new Date(firstDay);
    const dayOfWeek = firstDay.getDay(); // 0 = domingo, 1 = segunda, etc.
    startDate.setDate(firstDay.getDate() - dayOfWeek);

    const calendarGrid = document.getElementById('calendarGrid');
    if (!calendarGrid) return;

    // Remover apenas os dias, mantendo o cabeçalho
    const dayElements = calendarGrid.querySelectorAll('.calendar-day');
    dayElements.forEach(el => el.remove());

    // Gerar 42 dias (6 semanas x 7 dias)
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dayElement = document.createElement('div');
      dayElement.className = 'calendar-day';
      dayElement.textContent = currentDate.getDate();
      dayElement.style.textAlign = 'center';
      dayElement.style.cursor = 'pointer';

      // Verificar se é do mês atual
      if (currentDate.getMonth() === now.getMonth() && currentDate.getFullYear() === now.getFullYear()) {
        // Verificar se é hoje
        if (currentDate.getDate() === now.getDate()) {
          dayElement.classList.add('today');
        }
        dayElement.style.color = '#333';
      } else {
        // Dias de outros meses ficam mais claros
        dayElement.style.opacity = '0.4';
        dayElement.style.color = '#999';
      }

      calendarGrid.appendChild(dayElement);
    }
  }

  // Função para perguntar à IA
  function perguntarIA() {
    const pergunta = prompt('O que você gostaria de saber sobre seu negócio?');
    if (pergunta) {
      // Simular resposta da IA
      const respostas = [
        'Com base nos dados, recomendo focar nas vendas de Kit PCR COVID-19.',
        'Seu estoque de produtos está 15% abaixo do ideal para este período.',
        'As vendas no Mercado Livre estão 23% acima da média mensal.',
        'Considere criar uma promoção para produtos com baixa rotatividade.',
        'O melhor horário para postar produtos é entre 14h e 16h.'
      ];

      const resposta = respostas[Math.floor(Math.random() * respostas.length)];
      alert(`🤖 IA PCR Labor:\n\n${resposta}`);

      // Atualizar dica
      document.getElementById('aiTip').textContent = resposta;
    }
  }

  // Função para toggle de tarefas (NOVA)
  function toggleTask(checkbox) {
    const label = checkbox.nextElementSibling;
    const container = checkbox.parentElement;

    if (checkbox.checked) {
      label.style.textDecoration = 'line-through';
      container.style.background = '#dcfce7';
      container.style.opacity = '0.7';

      // Atualizar contador
      const counter = document.querySelector('[style*="background: #f59e0b"]');
      const currentCount = parseInt(counter.textContent);
      counter.textContent = Math.max(0, currentCount - 1);

      // Feedback visual
      setTimeout(() => {
        alert('✅ Tarefa concluída!\n\nParabéns por manter a produtividade!');
      }, 100);
    } else {
      label.style.textDecoration = 'none';
      container.style.background = '#f8f9fa';
      container.style.opacity = '1';

      // Atualizar contador
      const counter = document.querySelector('[style*="background: #f59e0b"]');
      const currentCount = parseInt(counter.textContent);
      counter.textContent = currentCount + 1;
    }
  }

  // Função para adicionar nova tarefa (NOVA)
  function adicionarTarefa() {
    const novaTarefa = prompt('📝 Nova Tarefa\n\nDigite a descrição da tarefa:');
    if (novaTarefa && novaTarefa.trim()) {
      alert(`✅ Tarefa adicionada!\n\n"${novaTarefa}"\n\nA tarefa foi adicionada à sua lista.`);

      // Atualizar contador
      const counter = document.querySelector('[style*="background: #f59e0b"]');
      const currentCount = parseInt(counter.textContent);
      counter.textContent = currentCount + 1;
    }
  }

  // Inicializar calendário quando a página carregar
  document.addEventListener('DOMContentLoaded', function() {
    generateCalendar();
  });
</script>
