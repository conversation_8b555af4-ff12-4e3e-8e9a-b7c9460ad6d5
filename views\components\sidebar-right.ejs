<!-- LATERAL DIREITA (SEMPRE PRESENTE) -->
<aside class="sidebar-right">
  <!-- Tarefas: Lista de tarefas rápidas -->
  <div class="tasks-section">
    <h3 class="section-title">
      <span>✅</span>
      <PERSON><PERSON>fas
    </h3>

    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task1">
      <label for="task1">Revisar pedidos pendentes</label>
    </div>

    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task2" checked>
      <label for="task2" style="text-decoration: line-through; opacity: 0.6;">Sincronizar Mercado Livre</label>
    </div>

    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task3">
      <label for="task3">Atualizar preços Shopee</label>
    </div>

    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task4">
      <label for="task4">Verificar estoque baixo</label>
    </div>

    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task5">
      <label for="task5">Enviar relatório mensal</label>
    </div>
  </div>

  <!-- Calendário: Pequeno calendário mensal -->
  <div class="calendar-section">
    <h3 class="section-title">
      <span>📅</span>
      Calendário
    </h3>

    <div class="mini-calendar">
      <div class="calendar-header" id="currentMonth">
        Janeiro 2024
      </div>

      <div class="calendar-grid" id="calendarGrid">
        <!-- Cabeçalho dos dias -->
        <div style="font-weight: bold; color: #666; text-align: center;">D</div>
        <div style="font-weight: bold; color: #666; text-align: center;">S</div>
        <div style="font-weight: bold; color: #666; text-align: center;">T</div>
        <div style="font-weight: bold; color: #666; text-align: center;">Q</div>
        <div style="font-weight: bold; color: #666; text-align: center;">Q</div>
        <div style="font-weight: bold; color: #666; text-align: center;">S</div>
        <div style="font-weight: bold; color: #666; text-align: center;">S</div>

        <!-- Dias do mês serão inseridos aqui via JavaScript -->
      </div>
    </div>
  </div>

  <!-- Botão "Pergunte para IA" -->
  <div class="ai-section">
    <h3 class="section-title">
      <span>🤖</span>
      Assistente IA
    </h3>

    <button class="ai-button" onclick="perguntarIA()">
      Pergunte para IA
    </button>

    <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666;">
      <strong>💡 Dica do dia:</strong><br>
      <span id="aiTip">Considere reabastecer o estoque de Kit PCR COVID-19. Vendas aumentaram 15% esta semana.</span>
    </div>
  </div>
</aside>

<script>
  // Gerar calendário (CORRIGIDO)
  function generateCalendar() {
    const now = new Date();
    const currentMonth = now.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    document.getElementById('currentMonth').textContent = currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1);

    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Calcular o primeiro dia da semana a ser mostrado
    const startDate = new Date(firstDay);
    const dayOfWeek = firstDay.getDay(); // 0 = domingo, 1 = segunda, etc.
    startDate.setDate(firstDay.getDate() - dayOfWeek);

    const calendarGrid = document.getElementById('calendarGrid');
    if (!calendarGrid) return;

    // Remover apenas os dias, mantendo o cabeçalho
    const dayElements = calendarGrid.querySelectorAll('.calendar-day');
    dayElements.forEach(el => el.remove());

    // Gerar 42 dias (6 semanas x 7 dias)
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dayElement = document.createElement('div');
      dayElement.className = 'calendar-day';
      dayElement.textContent = currentDate.getDate();
      dayElement.style.textAlign = 'center';
      dayElement.style.cursor = 'pointer';

      // Verificar se é do mês atual
      if (currentDate.getMonth() === now.getMonth() && currentDate.getFullYear() === now.getFullYear()) {
        // Verificar se é hoje
        if (currentDate.getDate() === now.getDate()) {
          dayElement.classList.add('today');
        }
        dayElement.style.color = '#333';
      } else {
        // Dias de outros meses ficam mais claros
        dayElement.style.opacity = '0.4';
        dayElement.style.color = '#999';
      }

      calendarGrid.appendChild(dayElement);
    }
  }

  // Função para perguntar à IA
  function perguntarIA() {
    const pergunta = prompt('O que você gostaria de saber sobre seu negócio?');
    if (pergunta) {
      // Simular resposta da IA
      const respostas = [
        'Com base nos dados, recomendo focar nas vendas de Kit PCR COVID-19.',
        'Seu estoque de produtos está 15% abaixo do ideal para este período.',
        'As vendas no Mercado Livre estão 23% acima da média mensal.',
        'Considere criar uma promoção para produtos com baixa rotatividade.',
        'O melhor horário para postar produtos é entre 14h e 16h.'
      ];

      const resposta = respostas[Math.floor(Math.random() * respostas.length)];
      alert(`🤖 IA PCR Labor:\n\n${resposta}`);

      // Atualizar dica
      document.getElementById('aiTip').textContent = resposta;
    }
  }

  // Inicializar calendário quando a página carregar
  document.addEventListener('DOMContentLoaded', function() {
    generateCalendar();
  });
</script>
