<!-- LATERAL DIREITA (SEMPRE PRESENTE) -->
<aside class="sidebar-right">
  <!-- Tarefas: Lista de tarefas rápidas -->
  <div class="tasks-section">
    <h3 class="section-title">
      <span>✅</span>
      <PERSON><PERSON>fas
    </h3>
    
    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task1">
      <label for="task1">Revisar pedidos pendentes</label>
    </div>
    
    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task2" checked>
      <label for="task2" style="text-decoration: line-through; opacity: 0.6;">Sincronizar Mercado Livre</label>
    </div>
    
    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task3">
      <label for="task3">Atualizar preços Shopee</label>
    </div>
    
    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task4">
      <label for="task4">Verificar estoque baixo</label>
    </div>
    
    <div class="task-item">
      <input type="checkbox" class="task-checkbox" id="task5">
      <label for="task5">Enviar relatório mensal</label>
    </div>
  </div>
  
  <!-- Calendário: Pequeno calendário mensal -->
  <div class="calendar-section">
    <h3 class="section-title">
      <span>📅</span>
      Calendário
    </h3>
    
    <div class="mini-calendar">
      <div class="calendar-header" id="currentMonth">
        Janeiro 2024
      </div>
      
      <div class="calendar-grid">
        <!-- Cabeçalho dos dias -->
        <div style="font-weight: bold; color: #666;">D</div>
        <div style="font-weight: bold; color: #666;">S</div>
        <div style="font-weight: bold; color: #666;">T</div>
        <div style="font-weight: bold; color: #666;">Q</div>
        <div style="font-weight: bold; color: #666;">Q</div>
        <div style="font-weight: bold; color: #666;">S</div>
        <div style="font-weight: bold; color: #666;">S</div>
        
        <!-- Dias do mês (será gerado via JavaScript) -->
        <div id="calendarDays">
          <!-- Dias serão inseridos aqui -->
        </div>
      </div>
    </div>
  </div>
  
  <!-- Botão "Pergunte para IA" -->
  <div class="ai-section">
    <h3 class="section-title">
      <span>🤖</span>
      Assistente IA
    </h3>
    
    <button class="ai-button" onclick="perguntarIA()">
      Pergunte para IA
    </button>
    
    <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666;">
      <strong>💡 Dica do dia:</strong><br>
      <span id="aiTip">Considere reabastecer o estoque de Kit PCR COVID-19. Vendas aumentaram 15% esta semana.</span>
    </div>
  </div>
</aside>

<script>
  // Gerar calendário
  function generateCalendar() {
    const now = new Date();
    const currentMonth = now.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    document.getElementById('currentMonth').textContent = currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1);
    
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const calendarDays = document.getElementById('calendarDays');
    calendarDays.innerHTML = '';
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      const dayElement = document.createElement('div');
      dayElement.className = 'calendar-day';
      dayElement.textContent = date.getDate();
      
      if (date.getMonth() === now.getMonth()) {
        if (date.getDate() === now.getDate()) {
          dayElement.classList.add('today');
        }
      } else {
        dayElement.style.opacity = '0.3';
      }
      
      calendarDays.appendChild(dayElement);
    }
  }
  
  // Função para perguntar à IA
  function perguntarIA() {
    const pergunta = prompt('O que você gostaria de saber sobre seu negócio?');
    if (pergunta) {
      // Simular resposta da IA
      const respostas = [
        'Com base nos dados, recomendo focar nas vendas de Kit PCR COVID-19.',
        'Seu estoque de produtos está 15% abaixo do ideal para este período.',
        'As vendas no Mercado Livre estão 23% acima da média mensal.',
        'Considere criar uma promoção para produtos com baixa rotatividade.',
        'O melhor horário para postar produtos é entre 14h e 16h.'
      ];
      
      const resposta = respostas[Math.floor(Math.random() * respostas.length)];
      alert(`🤖 IA PCR Labor:\n\n${resposta}`);
      
      // Atualizar dica
      document.getElementById('aiTip').textContent = resposta;
    }
  }
  
  // Inicializar calendário quando a página carregar
  document.addEventListener('DOMContentLoaded', function() {
    generateCalendar();
  });
</script>
