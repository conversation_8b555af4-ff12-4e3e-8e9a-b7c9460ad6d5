// routes/produtoRoutes.js

const express = require('express');
const router = express.Router();
const produtoController = require('../controllers/produtoController');

// GET /api/produtos - Buscar todos os produtos
router.get('/', produtoController.getAllProdutos);

// GET /api/produtos/:id - Buscar produto por ID
router.get('/:id', produtoController.getProdutoById);

// POST /api/produtos - Criar novo produto
router.post('/', produtoController.createProduto);

// PUT /api/produtos/:id - Atualizar produto
router.put('/:id', produtoController.updateProduto);

// PATCH /api/produtos/:id/estoque - Atualizar apenas estoque
router.patch('/:id/estoque', produtoController.updateEstoque);

// DELETE /api/produtos/:id - Deletar produto
router.delete('/:id', produtoController.deleteProduto);

module.exports = router;
