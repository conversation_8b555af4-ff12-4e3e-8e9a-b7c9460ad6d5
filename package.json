{"name": "mvc-boilerplate", "version": "1.0.0", "description": "Boilerplate para estrutura MVC em JavaScript com PostgreSQL e testes", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:coverage": "jest --coverage", "init-db": "node scripts/runSQLScript.js", "reset-db": "node scripts/resetDatabase.js"}, "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "dotenv": "^10.0.0", "ejs": "^3.1.10", "express": "^4.21.2", "express-session": "^1.18.1", "pg": "^8.16.0"}, "devDependencies": {"jest": "^27.0.6", "nodemon": "^2.0.22", "supertest": "^6.1.3"}, "directories": {"test": "tests"}, "repository": {"type": "git", "url": "git+https://github.com/c4uazinnnn/Projeto-Individual-Pcr-Labor.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/c4uazinnnn/Projeto-Individual-Pcr-Labor/issues"}, "homepage": "https://github.com/c4uazinnnn/Projeto-Individual-Pcr-Labor#readme"}