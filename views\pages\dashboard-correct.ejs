<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-correct.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="app-container">
    <!-- Navbar no Topo -->
    <%- include('../components/navbar') %>

    <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
    <main class="main-content">
      <!-- Header Simples -->
      <div style="margin-bottom: 30px;">
        <h1 style="font-size: 32px; font-weight: bold; color: #333; margin: 0;">Dashboard</h1>
      </div>

      <!-- Cards Principais (Seguindo Wireframe Exato) -->
      <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 40px;">
        <!-- Card 1: Vendas -->
        <div style="background: white; border-radius: 8px; padding: 25px; border: 1px solid #e0e0e0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
          <div style="width: 60px; height: 60px; background: #018820; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 24px;">
            💰
          </div>
          <h3 style="margin: 0 0 10px; color: #333; font-size: 18px;">Vendas</h3>
          <div style="font-size: 28px; font-weight: bold; color: #018820; margin-bottom: 5px;">
            <%= vendas.length %>
          </div>
          <p style="margin: 0; color: #666; font-size: 14px;">vendas realizadas</p>
        </div>

        <!-- Card 2: Estoque -->
        <div style="background: white; border-radius: 8px; padding: 25px; border: 1px solid #e0e0e0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
          <div style="width: 60px; height: 60px; background: #f59e0b; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 24px;">
            📦
          </div>
          <h3 style="margin: 0 0 10px; color: #333; font-size: 18px;">Estoque</h3>
          <div style="font-size: 28px; font-weight: bold; color: #f59e0b; margin-bottom: 5px;">
            <%= produtos.reduce((total, p) => total + p.estoque_atual, 0) %>
          </div>
          <p style="margin: 0; color: #666; font-size: 14px;">unidades em estoque</p>
        </div>

        <!-- Card 3: Pedidos -->
        <div style="background: white; border-radius: 8px; padding: 25px; border: 1px solid #e0e0e0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
          <div style="width: 60px; height: 60px; background: #3b82f6; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 24px;">
            📋
          </div>
          <h3 style="margin: 0 0 10px; color: #333; font-size: 18px;">Pedidos</h3>
          <div style="font-size: 28px; font-weight: bold; color: #3b82f6; margin-bottom: 5px;">
            12
          </div>
          <p style="margin: 0; color: #666; font-size: 14px;">pedidos pendentes</p>
        </div>

        <!-- Card 4: Plataformas -->
        <div style="background: white; border-radius: 8px; padding: 25px; border: 1px solid #e0e0e0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
          <div style="width: 60px; height: 60px; background: #8b5cf6; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 24px;">
            🌐
          </div>
          <h3 style="margin: 0 0 10px; color: #333; font-size: 18px;">Plataformas</h3>
          <div style="font-size: 28px; font-weight: bold; color: #8b5cf6; margin-bottom: 5px;">
            3
          </div>
          <p style="margin: 0; color: #666; font-size: 14px;">plataformas ativas</p>
        </div>
      </div>

      <!-- Seção Principal (Seguindo Wireframe) -->
      <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 30px; margin-bottom: 40px;">
        <!-- Gráfico Principal -->
        <div style="background: white; border-radius: 8px; padding: 25px; border: 1px solid #e0e0e0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h3 style="margin: 0 0 20px; color: #333; font-size: 18px;">📈 Vendas dos Últimos 30 Dias</h3>
          <div style="height: 300px;">
            <canvas id="vendasChart"></canvas>
          </div>
        </div>

        <!-- Resumo Rápido -->
        <div style="background: white; border-radius: 8px; padding: 25px; border: 1px solid #e0e0e0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h3 style="margin: 0 0 20px; color: #333; font-size: 18px;">📊 Resumo</h3>
          <div style="display: grid; gap: 15px;">
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
              <div style="font-size: 20px; font-weight: bold; color: #018820;">R$ <%= stats.valorTotalVendas.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ".") %></div>
              <div style="font-size: 14px; color: #666;">Faturamento Total</div>
            </div>
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
              <div style="font-size: 20px; font-weight: bold; color: #f59e0b;"><%= stats.produtosEstoqueBaixo %></div>
              <div style="font-size: 14px; color: #666;">Produtos com Estoque Baixo</div>
            </div>
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
              <div style="font-size: 20px; font-weight: bold; color: #3b82f6;"><%= stats.vendasHoje %></div>
              <div style="font-size: 14px; color: #666;">Vendas Hoje</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Listas Simples (Seguindo Wireframe) -->
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
        <!-- Produtos -->
        <div style="background: white; border-radius: 8px; padding: 25px; border: 1px solid #e0e0e0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
            <h3 style="margin: 0; color: #333; font-size: 18px;">🧪 Top Produtos</h3>
            <a href="/produtos" style="color: #018820; text-decoration: none; font-size: 14px;">Ver todos →</a>
          </div>
          <div style="display: grid; gap: 12px;">
            <% if (produtos && produtos.length > 0) { %>
              <% produtos.slice(0, 4).forEach(produto => { %>
                <div style="display: flex; justify-content: between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                  <div>
                    <div style="font-weight: 600; color: #333; font-size: 14px;"><%= produto.nome %></div>
                    <div style="color: #666; font-size: 12px;">Estoque: <%= produto.estoque_atual %></div>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-weight: bold; color: #018820;">R$ <%= parseFloat(produto.preco).toFixed(2) %></div>
                  </div>
                </div>
              <% }) %>
            <% } else { %>
              <div style="text-align: center; color: #666; padding: 20px;">Nenhum produto</div>
            <% } %>
          </div>
        </div>

        <!-- Vendas -->
        <div style="background: white; border-radius: 8px; padding: 25px; border: 1px solid #e0e0e0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
            <h3 style="margin: 0; color: #333; font-size: 18px;">💰 Vendas Recentes</h3>
            <a href="/vendas" style="color: #018820; text-decoration: none; font-size: 14px;">Ver todas →</a>
          </div>
          <div style="display: grid; gap: 12px;">
            <% if (vendas && vendas.length > 0) { %>
              <% vendas.slice(0, 4).forEach(venda => { %>
                <div style="display: flex; justify-content: between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                  <div>
                    <div style="font-weight: 600; color: #333; font-size: 14px;"><%= venda.produto_nome || 'Produto N/A' %></div>
                    <div style="color: #666; font-size: 12px;"><%= new Date(venda.data).toLocaleDateString('pt-BR') %></div>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-weight: bold; color: #018820;">R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></div>
                  </div>
                </div>
              <% }) %>
            <% } else { %>
              <div style="text-align: center; color: #666; padding: 20px;">Nenhuma venda</div>
            <% } %>
          </div>
        </div>
      </div>

    </main>
  </div>

  <script>
    // Gráfico simples de vendas
    const vendasCtx = document.getElementById('vendasChart').getContext('2d');

    // Dados dos últimos 7 dias (simples)
    const labels = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
    const data = [12, 19, 8, 15, 22, 18, 25];

    new Chart(vendasCtx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Vendas',
          data: data,
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#018820',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            },
            ticks: {
              stepSize: 5
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });

    console.log('📊 Dashboard carregado');
  </script>
</body>
</html>
