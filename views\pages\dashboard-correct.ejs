<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-final.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- Header horizontal (Topo) com logo e menu -->
  <%- include('../components/header-horizontal') %>

  <!-- Layout principal SEM SIDEBAR -->
  <div style="margin-top: 70px; padding: 30px;">
    <!-- Área central COMPLETA -->
    <h1 class="page-title">Dashboard - Visão Geral do Sistema</h1>

    <!-- BLOCO 1: RESUMO DE VENDAS (da tela de vendas) -->
    <div class="content-section">
      <div class="section-header">
        <h3 class="section-title">💰 Vendas - Resumo</h3>
        <a href="/vendas" class="btn btn-primary">Ver <PERSON></a>
      </div>
      <div class="section-content">
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
          <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #018820;">R$ <%= stats.valorTotalVendas.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ".") %></div>
            <div style="color: #666; font-size: 14px;">Total de Vendas</div>
          </div>
          <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #3b82f6;"><%= vendas.length %></div>
            <div style="color: #666; font-size: 14px;">Vendas Realizadas</div>
          </div>
          <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #16a34a;"><%= vendas.filter(v => new Date(v.data).toDateString() === new Date().toDateString()).length %></div>
            <div style="color: #666; font-size: 14px;">Vendas Hoje</div>
          </div>
          <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #f59e0b;">+12%</div>
            <div style="color: #666; font-size: 14px;">Crescimento</div>
          </div>
        </div>
        <div style="height: 200px;">
          <canvas id="vendasChart"></canvas>
        </div>
      </div>
    </div>

    <!-- BLOCO 2: RESUMO DE ESTOQUE (da tela de estoque) -->
    <div class="content-section">
      <div class="section-header">
        <h3 class="section-title">📦 Estoque - Status</h3>
        <a href="/estoque" class="btn btn-primary">Ver Detalhes</a>
      </div>
      <div class="section-content">
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 32px; font-weight: bold; color: #16a34a;"><%= produtos.reduce((total, p) => total + p.estoque_atual, 0) %></div>
            <div style="color: #666; font-size: 14px; margin-bottom: 10px;">Total em Estoque</div>
            <div style="height: 100px;">
              <canvas id="estoqueChart"></canvas>
            </div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 32px; font-weight: bold; color: #f59e0b;"><%= stats.produtosEstoqueBaixo %></div>
            <div style="color: #666; font-size: 14px; margin-bottom: 10px;">Estoque Baixo</div>
            <div style="height: 100px;">
              <canvas id="comprasChart"></canvas>
            </div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 32px; font-weight: bold; color: #3b82f6;">28</div>
            <div style="color: #666; font-size: 14px; margin-bottom: 10px;">A Enviar</div>
            <div style="height: 100px;">
              <canvas id="enviarChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- BLOCO 3: RESUMO DE PEDIDOS (da tela de pedidos) -->
    <div class="content-section">
      <div class="section-header">
        <h3 class="section-title">📋 Pedidos - Status</h3>
        <a href="/pedidos" class="btn btn-primary">Ver Detalhes</a>
      </div>
      <div class="section-content">
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
          <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #f59e0b;">12</div>
            <div style="color: #666; font-size: 14px;">Pendentes</div>
          </div>
          <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #16a34a;">8</div>
            <div style="color: #666; font-size: 14px;">Aprovados</div>
          </div>
          <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #3b82f6;">R$ 15.400</div>
            <div style="color: #666; font-size: 14px;">Valor Total</div>
          </div>
          <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #8b5cf6;">5-7 dias</div>
            <div style="color: #666; font-size: 14px;">Prazo Médio</div>
          </div>
        </div>
        <div style="height: 150px;">
          <canvas id="pedidosChart"></canvas>
        </div>
      </div>
    </div>

    <!-- BLOCO 4: RESUMO DE PLATAFORMAS (da tela de plataformas) -->
    <div class="content-section">
      <div class="section-header">
        <h3 class="section-title">🌐 Plataformas - Performance</h3>
        <a href="/plataformas" class="btn btn-primary">Ver Detalhes</a>
      </div>
      <div class="section-content">
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #ff6600;">
            <div style="font-size: 20px; font-weight: bold; color: #333; margin-bottom: 10px;">Shopee</div>
            <div style="font-size: 24px; font-weight: bold; color: #ff6600;">R$ 18.500</div>
            <div style="color: #666; font-size: 14px; margin-bottom: 10px;">Vendas do Mês</div>
            <div style="color: #16a34a; font-size: 12px; font-weight: bold;">+15% vs mês anterior</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #3483fa;">
            <div style="font-size: 20px; font-weight: bold; color: #333; margin-bottom: 10px;">Mercado Livre</div>
            <div style="font-size: 24px; font-weight: bold; color: #3483fa;">R$ 22.300</div>
            <div style="color: #666; font-size: 14px; margin-bottom: 10px;">Vendas do Mês</div>
            <div style="color: #16a34a; font-size: 12px; font-weight: bold;">+8% vs mês anterior</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #018820;">
            <div style="font-size: 20px; font-weight: bold; color: #333; margin-bottom: 10px;">PCR Labor</div>
            <div style="font-size: 24px; font-weight: bold; color: #018820;">R$ 12.800</div>
            <div style="color: #666; font-size: 14px; margin-bottom: 10px;">Vendas do Mês</div>
            <div style="color: #16a34a; font-size: 12px; font-weight: bold;">+25% vs mês anterior</div>
          </div>
        </div>
      </div>
    </div>

  </div>

  <script>
    // Gráfico simples de vendas
    const vendasCtx = document.getElementById('vendasChart').getContext('2d');

    // Dados dos últimos 7 dias (simples)
    const labels = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
    const data = [12, 19, 8, 15, 22, 18, 25];

    new Chart(vendasCtx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Vendas',
          data: data,
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#018820',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            },
            ticks: {
              stepSize: 5
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });

    // GRÁFICO 2: Estoque (doughnut)
    const estoqueCtx = document.getElementById('estoqueChart').getContext('2d');
    new Chart(estoqueCtx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [70, 20, 10],
          backgroundColor: ['#16a34a', '#f59e0b', '#ef4444'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: false } }
      }
    });

    // GRÁFICO 3: Compras (doughnut)
    const comprasCtx = document.getElementById('comprasChart').getContext('2d');
    new Chart(comprasCtx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [60, 30, 10],
          backgroundColor: ['#f59e0b', '#3b82f6', '#ef4444'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: false } }
      }
    });

    // GRÁFICO 4: A Enviar (doughnut)
    const enviarCtx = document.getElementById('enviarChart').getContext('2d');
    new Chart(enviarCtx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [40, 35, 25],
          backgroundColor: ['#3b82f6', '#8b5cf6', '#16a34a'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: false } }
      }
    });

    // GRÁFICO 5: Pedidos (bar)
    const pedidosCtx = document.getElementById('pedidosChart').getContext('2d');
    new Chart(pedidosCtx, {
      type: 'bar',
      data: {
        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai'],
        datasets: [{
          data: [8, 12, 6, 15, 18],
          backgroundColor: '#3b82f6',
          borderRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: false } },
        scales: {
          y: { beginAtZero: true, display: false },
          x: { display: false }
        }
      }
    });

    // Função para sincronizar tudo
    function sincronizarTudo() {
      alert('🔄 Sincronizando todas as plataformas...\n\n• Mercado Livre\n• Shopee\n• PCR Labor\n\nEsta operação pode levar alguns minutos.');

      setTimeout(() => {
        alert('✅ Sincronização concluída!\n\n• 5 novas vendas encontradas\n• 3 produtos atualizados\n• 2 pedidos processados');
        location.reload();
      }, 3000);
    }

    console.log('📊 Dashboard com blocos simplificados de cada tela carregado');
  </script>
</body>
</html>
