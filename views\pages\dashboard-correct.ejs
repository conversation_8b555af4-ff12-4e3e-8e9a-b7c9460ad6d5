<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-correct.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="app-container">
    <!-- Navbar no Topo -->
    <%- include('../components/navbar') %>
    
    <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
    <main class="main-content">
      <!-- Header da Página -->
      <div class="page-header">
        <h1 class="page-title">Dashboard</h1>
        <p class="page-subtitle">Visão geral do seu negócio em tempo real</p>
      </div>
      
      <!-- Métricas Principais (Objetivas) -->
      <div class="metrics-grid">
        <!-- Total de Vendas -->
        <div class="metric-card">
          <div class="metric-header">
            <div class="metric-icon">💰</div>
            <div class="metric-trend trend-up">+12%</div>
          </div>
          <div class="metric-value">R$ <%= stats.valorTotalVendas.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ".") %></div>
          <div class="metric-label">Faturamento Total</div>
        </div>
        
        <!-- Vendas Hoje -->
        <div class="metric-card">
          <div class="metric-header">
            <div class="metric-icon">📈</div>
            <div class="metric-trend trend-up">+8%</div>
          </div>
          <div class="metric-value"><%= stats.vendasHoje %></div>
          <div class="metric-label">Vendas Hoje</div>
        </div>
        
        <!-- Produtos em Estoque -->
        <div class="metric-card">
          <div class="metric-header">
            <div class="metric-icon">📦</div>
            <div class="metric-trend trend-down">-3%</div>
          </div>
          <div class="metric-value"><%= stats.totalProdutos %></div>
          <div class="metric-label">Produtos Ativos</div>
        </div>
        
        <!-- Estoque Baixo -->
        <div class="metric-card">
          <div class="metric-header">
            <div class="metric-icon">⚠️</div>
            <div class="metric-trend trend-down">-15%</div>
          </div>
          <div class="metric-value"><%= stats.produtosEstoqueBaixo %></div>
          <div class="metric-label">Estoque Baixo</div>
        </div>
      </div>
      
      <!-- Gráficos -->
      <div class="charts-grid">
        <!-- Gráfico Principal -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">Vendas dos Últimos 30 Dias</h3>
            <button class="btn btn-secondary">📊 Ver Relatório</button>
          </div>
          <div class="section-content">
            <div class="chart-container">
              <canvas id="vendasChart"></canvas>
            </div>
          </div>
        </div>
        
        <!-- Status do Estoque -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">Status do Estoque</h3>
          </div>
          <div class="section-content">
            <div class="chart-container">
              <canvas id="estoqueChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Tabelas Resumidas -->
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <!-- Top Produtos -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">Top 5 Produtos</h3>
            <a href="/produtos" class="btn btn-secondary">Ver Todos</a>
          </div>
          <div class="section-content">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Produto</th>
                  <th>Vendas</th>
                  <th>Estoque</th>
                </tr>
              </thead>
              <tbody>
                <% if (produtos && produtos.length > 0) { %>
                  <% produtos.slice(0, 5).forEach(produto => { %>
                    <tr>
                      <td><strong><%= produto.nome %></strong></td>
                      <td>
                        <% 
                          const vendasProduto = vendas.filter(v => v.id_produto == produto.id_produto).length;
                        %>
                        <%= vendasProduto %> vendas
                      </td>
                      <td>
                        <% if (produto.estoque_atual <= 10) { %>
                          <span style="color: #dc2626; font-weight: bold;"><%= produto.estoque_atual %></span>
                        <% } else { %>
                          <span style="color: #16a34a;"><%= produto.estoque_atual %></span>
                        <% } %>
                      </td>
                    </tr>
                  <% }) %>
                <% } else { %>
                  <tr>
                    <td colspan="3" style="text-align: center; color: #666;">Nenhum produto encontrado</td>
                  </tr>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Vendas Recentes -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">Vendas Recentes</h3>
            <a href="/vendas" class="btn btn-secondary">Ver Todas</a>
          </div>
          <div class="section-content">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Data</th>
                  <th>Produto</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
                <% if (vendas && vendas.length > 0) { %>
                  <% vendas.slice(0, 5).forEach(venda => { %>
                    <tr>
                      <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                      <td><%= venda.produto_nome || 'N/A' %></td>
                      <td><strong>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></strong></td>
                    </tr>
                  <% }) %>
                <% } else { %>
                  <tr>
                    <td colspan="3" style="text-align: center; color: #666;">Nenhuma venda encontrada</td>
                  </tr>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- Alertas Importantes -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">🚨 Alertas Importantes</h3>
        </div>
        <div class="section-content">
          <div style="display: grid; gap: 15px;">
            <% if (stats.produtosEstoqueBaixo > 0) { %>
              <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 15px;">
                <strong>⚠️ Estoque Baixo:</strong> <%= stats.produtosEstoqueBaixo %> produtos precisam de reposição
                <a href="/estoque" style="margin-left: 10px; color: #d97706;">Ver Detalhes →</a>
              </div>
            <% } %>
            
            <div style="background: #dcfce7; border: 1px solid #16a34a; border-radius: 8px; padding: 15px;">
              <strong>✅ Sistema Online:</strong> Todas as integrações funcionando normalmente
            </div>
            
            <div style="background: #dbeafe; border: 1px solid #3b82f6; border-radius: 8px; padding: 15px;">
              <strong>📊 Relatório:</strong> Vendas aumentaram 12% comparado ao mês passado
              <a href="/vendas" style="margin-left: 10px; color: #2563eb;">Ver Análise →</a>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script>
    // Dados para gráficos
    const produtosData = <%- JSON.stringify(produtos) %>;
    const vendasData = <%- JSON.stringify(vendas) %>;

    // Gráfico de Vendas (Linha simples e objetiva)
    const vendasCtx = document.getElementById('vendasChart').getContext('2d');
    
    // Simular dados dos últimos 30 dias
    const labels = [];
    const data = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      labels.push(date.getDate() + '/' + (date.getMonth() + 1));
      data.push(Math.floor(Math.random() * 20) + 5); // Dados simulados
    }

    new Chart(vendasCtx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Vendas',
          data: data,
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });

    // Gráfico de Estoque (Doughnut simples)
    const estoqueCtx = document.getElementById('estoqueChart').getContext('2d');
    const produtosNormal = produtosData.filter(p => p.estoque_atual > 10).length;
    const produtosBaixo = produtosData.filter(p => p.estoque_atual <= 10 && p.estoque_atual > 0).length;
    const produtosZero = produtosData.filter(p => p.estoque_atual === 0).length;

    new Chart(estoqueCtx, {
      type: 'doughnut',
      data: {
        labels: ['Normal', 'Baixo', 'Zero'],
        datasets: [{
          data: [produtosNormal, produtosBaixo, produtosZero],
          backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });

    console.log('📊 Dashboard carregado com sucesso');
  </script>
</body>
</html>
