<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/dashboard.css">
</head>
<body>
  <%- include('../components/sidebar') %>
  
  <div class="main-content">
    <%- include('../components/topbar') %>
    
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>Produtos</h1>
        <p>Gerenciamento de produtos PCR Labor</p>
      </div>
      
      <!-- Botão para adicionar produto -->
      <div class="action-bar">
        <button class="btn btn-primary" onclick="openAddProductModal()">
          ➕ Adicionar Produto
        </button>
        <div class="search-filters">
          <input type="text" placeholder="Buscar produtos..." id="productSearch">
          <select id="categoryFilter">
            <option value="">Todas as categorias</option>
            <option value="pcr">Kits PCR</option>
            <option value="reagentes">Reagentes</option>
            <option value="equipamentos">Equipamentos</option>
          </select>
        </div>
      </div>
      
      <!-- Tabela de produtos -->
      <div class="dashboard-card">
        <h3>Lista de Produtos</h3>
        <div class="table-container">
          <table id="productsTable">
            <thead>
              <tr>
                <th>SKU</th>
                <th>Nome</th>
                <th>Preço</th>
                <th>Estoque</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% produtos.forEach(produto => { %>
                <tr>
                  <td><%= produto.sku %></td>
                  <td><%= produto.nome %></td>
                  <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                  <td class="<%= produto.estoque_atual <= 10 ? 'low-stock' : '' %>">
                    <%= produto.estoque_atual %>
                  </td>
                  <td>
                    <span class="status-badge <%= produto.estoque_atual > 0 ? 'active' : 'inactive' %>">
                      <%= produto.estoque_atual > 0 ? 'Ativo' : 'Sem Estoque' %>
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-icon" onclick="editProduct(<%= produto.id_produto %>)" title="Editar">
                        ✏️
                      </button>
                      <button class="btn-icon" onclick="viewProduct(<%= produto.id_produto %>)" title="Visualizar">
                        👁️
                      </button>
                      <button class="btn-icon delete" onclick="deleteProduct(<%= produto.id_produto %>)" title="Excluir">
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para adicionar/editar produto -->
  <div id="productModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modalTitle">Adicionar Produto</h3>
        <span class="close" onclick="closeProductModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="productForm">
          <div class="form-group">
            <label for="productName">Nome do Produto:</label>
            <input type="text" id="productName" name="nome" required>
          </div>
          
          <div class="form-group">
            <label for="productSku">SKU:</label>
            <input type="text" id="productSku" name="sku" required>
          </div>
          
          <div class="form-group">
            <label for="productPrice">Preço:</label>
            <input type="number" id="productPrice" name="preco" step="0.01" required>
          </div>
          
          <div class="form-group">
            <label for="productStock">Estoque Atual:</label>
            <input type="number" id="productStock" name="estoque_atual" required>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="closeProductModal()">
              Cancelar
            </button>
            <button type="submit" class="btn btn-primary">
              Salvar
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <style>
    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 20px;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .search-filters {
      display: flex;
      gap: 15px;
    }
    
    .search-filters input,
    .search-filters select {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    
    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    
    .btn-primary {
      background-color: #018820;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #016a1a;
    }
    
    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background-color: #545b62;
    }
    
    .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
    }
    
    .status-badge.active {
      background-color: #d4edda;
      color: #155724;
    }
    
    .status-badge.inactive {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    .action-buttons {
      display: flex;
      gap: 5px;
    }
    
    .btn-icon {
      background: none;
      border: none;
      cursor: pointer;
      padding: 5px;
      border-radius: 3px;
      transition: background-color 0.3s;
    }
    
    .btn-icon:hover {
      background-color: #f8f9fa;
    }
    
    .btn-icon.delete:hover {
      background-color: #f8d7da;
    }
    
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
      background-color: white;
      margin: 5% auto;
      padding: 0;
      border-radius: 10px;
      width: 90%;
      max-width: 500px;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #eee;
    }
    
    .modal-header h3 {
      margin: 0;
      color: #333;
    }
    
    .close {
      font-size: 24px;
      cursor: pointer;
      color: #999;
    }
    
    .close:hover {
      color: #333;
    }
    
    .modal-body {
      padding: 20px;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #333;
    }
    
    .form-group input {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 14px;
    }
    
    .form-group input:focus {
      outline: none;
      border-color: #018820;
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }
  </style>

  <script>
    function openAddProductModal() {
      document.getElementById('modalTitle').textContent = 'Adicionar Produto';
      document.getElementById('productForm').reset();
      document.getElementById('productModal').style.display = 'block';
    }
    
    function closeProductModal() {
      document.getElementById('productModal').style.display = 'none';
    }
    
    function editProduct(id) {
      // Implementar edição
      alert('Editar produto ID: ' + id);
    }
    
    function viewProduct(id) {
      // Implementar visualização
      alert('Visualizar produto ID: ' + id);
    }
    
    function deleteProduct(id) {
      if (confirm('Tem certeza que deseja excluir este produto?')) {
        // Implementar exclusão
        alert('Excluir produto ID: ' + id);
      }
    }
    
    // Fechar modal ao clicar fora
    window.onclick = function(event) {
      const modal = document.getElementById('productModal');
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    }
    
    // Filtros e busca
    document.getElementById('productSearch').addEventListener('input', function() {
      // Implementar busca
    });
    
    document.getElementById('categoryFilter').addEventListener('change', function() {
      // Implementar filtro
    });
  </script>
</body>
</html>
