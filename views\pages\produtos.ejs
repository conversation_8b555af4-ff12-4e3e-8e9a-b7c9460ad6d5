<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe.css">
</head>
<body>
  <div class="app-container">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content -->
    <div class="main-content">
      <!-- Topbar -->
      <%- include('../components/topbar') %>
      
      <!-- Content Area -->
      <div class="content-area">
        <!-- Page Header -->
        <div class="page-header">
          <h1 class="page-title">Produtos</h1>
          <p class="page-subtitle">Gerenciamento de produtos PCR Labor</p>
        </div>
        
        <!-- Ações e Filtros -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <div style="display: flex; gap: 15px;">
            <input 
              type="text" 
              placeholder="Buscar produtos..." 
              style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; width: 300px;"
              id="searchProducts"
            >
            <select style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
              <option value="">Todos os produtos</option>
              <option value="covid">Kit PCR COVID</option>
              <option value="flu">Kit PCR Influenza</option>
              <option value="hepatite">Kit PCR Hepatite</option>
            </select>
          </div>
          <button class="btn btn-primary" onclick="openAddProductModal()">
            ➕ Novo Produto
          </button>
        </div>
        
        <!-- Cards de Resumo -->
        <div class="cards-grid">
          <div class="card">
            <div class="card-header">
              <div class="card-icon">🧪</div>
              <div>
                <h3 class="card-title">Total de Produtos</h3>
              </div>
            </div>
            <div class="card-value"><%= produtos.length %></div>
            <div class="card-trend">Produtos cadastrados</div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #fff3cd;">⚠️</div>
              <div>
                <h3 class="card-title">Estoque Baixo</h3>
              </div>
            </div>
            <div class="card-value" style="color: #856404;">
              <%= produtos.filter(p => p.estoque_atual <= 10).length %>
            </div>
            <div class="card-trend">Produtos com estoque baixo</div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #d4edda;">💰</div>
              <div>
                <h3 class="card-title">Valor Total</h3>
              </div>
            </div>
            <div class="card-value" style="color: #155724;">
              R$ <%= produtos.reduce((total, p) => total + (parseFloat(p.preco) * p.estoque_atual), 0).toFixed(2) %>
            </div>
            <div class="card-trend">Valor total em estoque</div>
          </div>
        </div>
        
        <!-- Tabela de Produtos -->
        <div class="table-container">
          <div class="table-header">
            <h3 class="table-title">🧪 Lista de Produtos</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Produto</th>
                <th>SKU</th>
                <th>Preço</th>
                <th>Estoque</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (produtos && produtos.length > 0) { %>
                <% produtos.forEach(produto => { %>
                  <tr>
                    <td>
                      <strong><%= produto.nome %></strong>
                    </td>
                    <td>
                      <code style="background: #f0f0f0; padding: 2px 6px; border-radius: 4px;">
                        <%= produto.sku %>
                      </code>
                    </td>
                    <td>
                      <strong>R$ <%= parseFloat(produto.preco).toFixed(2) %></strong>
                    </td>
                    <td>
                      <span style="color: <%= produto.estoque_atual <= 10 ? '#856404' : '#155724' %>;">
                        <%= produto.estoque_atual %> unidades
                      </span>
                    </td>
                    <td>
                      <% if (produto.estoque_atual <= 10) { %>
                        <span style="background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                          ⚠️ Estoque Baixo
                        </span>
                      <% } else { %>
                        <span style="background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                          ✅ Em Estoque
                        </span>
                      <% } %>
                    </td>
                    <td>
                      <div style="display: flex; gap: 8px;">
                        <button 
                          class="btn btn-secondary" 
                          style="padding: 6px 12px; font-size: 12px;"
                          onclick="editProduct(<%= produto.id_produto %>)"
                        >
                          ✏️ Editar
                        </button>
                        <button 
                          class="btn btn-secondary" 
                          style="padding: 6px 12px; font-size: 12px;"
                          onclick="updateStock(<%= produto.id_produto %>)"
                        >
                          📦 Estoque
                        </button>
                      </div>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="6" style="text-align: center; color: #666; padding: 40px;">
                    <div>
                      <div style="font-size: 48px; margin-bottom: 16px;">🧪</div>
                      <div style="font-size: 18px; margin-bottom: 8px;">Nenhum produto encontrado</div>
                      <div style="font-size: 14px; color: #999;">Clique em "Novo Produto" para adicionar o primeiro produto</div>
                    </div>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para Novo Produto -->
  <div id="productModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 30px; width: 90%; max-width: 500px;">
      <h3 style="margin-bottom: 20px;">➕ Novo Produto</h3>
      
      <form id="productForm">
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600;">Nome do Produto</label>
          <input type="text" name="nome" required style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px;">
        </div>
        
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600;">SKU</label>
          <input type="text" name="sku" required style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px;">
        </div>
        
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600;">Preço (R$)</label>
          <input type="number" name="preco" step="0.01" required style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px;">
        </div>
        
        <div style="margin-bottom: 30px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600;">Estoque Inicial</label>
          <input type="number" name="estoque_atual" required style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px;">
        </div>
        
        <div style="display: flex; gap: 15px; justify-content: flex-end;">
          <button type="button" class="btn btn-secondary" onclick="closeProductModal()">
            Cancelar
          </button>
          <button type="submit" class="btn btn-primary">
            ➕ Adicionar Produto
          </button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // Funcionalidades da página de produtos
    function openAddProductModal() {
      document.getElementById('productModal').style.display = 'block';
    }
    
    function closeProductModal() {
      document.getElementById('productModal').style.display = 'none';
      document.getElementById('productForm').reset();
    }
    
    function editProduct(id) {
      alert('Editar produto ID: ' + id);
      // Implementar edição
    }
    
    function updateStock(id) {
      const newStock = prompt('Nova quantidade em estoque:');
      if (newStock !== null && !isNaN(newStock)) {
        // Implementar atualização de estoque
        alert('Estoque atualizado para: ' + newStock);
      }
    }
    
    // Busca de produtos
    document.getElementById('searchProducts').addEventListener('input', function(e) {
      const searchTerm = e.target.value.toLowerCase();
      const rows = document.querySelectorAll('.data-table tbody tr');
      
      rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
      });
    });
    
    // Submissão do formulário
    document.getElementById('productForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const formData = new FormData(e.target);
      const productData = Object.fromEntries(formData);
      
      try {
        const response = await fetch('/api/produtos', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(productData)
        });
        
        if (response.ok) {
          alert('✅ Produto adicionado com sucesso!');
          closeProductModal();
          window.location.reload();
        } else {
          alert('❌ Erro ao adicionar produto');
        }
      } catch (error) {
        alert('❌ Erro de conexão: ' + error.message);
      }
    });
    
    // Fechar modal ao clicar fora
    document.getElementById('productModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeProductModal();
      }
    });
  </script>
</body>
</html>
