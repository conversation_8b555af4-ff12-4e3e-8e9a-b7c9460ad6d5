<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-correct.css">
</head>
<body>
  <div class="app-container">
    <%- include('../components/navbar') %>

    <main class="main-content">
      <div class="page-header">
        <h1 class="page-title">Produtos</h1>
        <p class="page-subtitle">Gestão completa do catálogo de produtos</p>
      </div>

      <div class="content-area">
        <!-- Cards de Métricas -->
        <div class="metrics-grid">
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">🧪</div>
              <h3 class="card-title">Total de Produtos</h3>
            </div>
            <div class="card-value"><%= produtos.length %></div>
            <div class="card-subtitle">produtos cadastrados</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">⚠️</div>
              <h3 class="card-title">Estoque Baixo</h3>
            </div>
            <div class="card-value"><%= produtos.filter(p => p.estoque_atual <= 10).length %></div>
            <div class="card-subtitle">produtos com estoque baixo</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">💰</div>
              <h3 class="card-title">Valor Total</h3>
            </div>
            <div class="card-value">R$ <%= produtos.reduce((total, p) => total + (parseFloat(p.preco) * p.estoque_atual), 0).toFixed(2) %></div>
            <div class="card-subtitle">valor em estoque</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📊</div>
              <h3 class="card-title">Preço Médio</h3>
            </div>
            <div class="card-value">R$ <%= produtos.length > 0 ? (produtos.reduce((total, p) => total + parseFloat(p.preco), 0) / produtos.length).toFixed(2) : '0.00' %></div>
            <div class="card-subtitle">preço médio dos produtos</div>
          </div>
        </div>

        <!-- Tabela de Produtos -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Lista de Produtos</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Nome</th>
                <th>SKU</th>
                <th>Preço</th>
                <th>Estoque</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (produtos && produtos.length > 0) { %>
                <% produtos.forEach(produto => { %>
                  <tr>
                    <td><%= produto.nome %></td>
                    <td><%= produto.sku %></td>
                    <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                    <td><%= produto.estoque_atual %></td>
                    <td>
                      <% if (produto.estoque_atual <= 10) { %>
                        <span style="color: #dc3545;">Estoque Baixo</span>
                      <% } else { %>
                        <span style="color: #28a745;">Normal</span>
                      <% } %>
                    </td>
                    <td>
                      <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">
                        Editar
                      </button>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="6" style="text-align: center; color: #666; padding: 40px;">
                    Nenhum produto encontrado
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
    </main>
  </div>
</body>
</html>
