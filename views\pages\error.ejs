<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
</head>
<body>
  <div style="min-height: 100vh; display: flex; align-items: center; justify-content: center; background: #f8f9fa;">
    <div style="text-align: center; max-width: 500px; padding: 40px;">
      <div style="font-size: 72px; margin-bottom: 20px;">😵</div>
      <h1 style="font-size: 32px; color: #018820; margin-bottom: 16px;">Erro</h1>
      <h2 style="font-size: 18px; color: #333; margin-bottom: 20px;">Algo deu errado</h2>
      
      <% if (typeof error !== 'undefined') { %>
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
          <strong>Erro:</strong> <%= error %>
        </div>
      <% } %>
      
      <p style="color: #666; margin-bottom: 30px;">
        Desculpe, ocorreu um erro inesperado.
      </p>
      
      <div style="display: flex; gap: 15px; justify-content: center;">
        <a href="/dashboard" class="btn btn-primary">
          Voltar ao Dashboard
        </a>
        <button class="btn btn-secondary" onclick="window.location.reload()">
          Tentar Novamente
        </button>
      </div>
    </div>
  </div>
</body>
</html>
