<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/style.css">
  <style>
    .error-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #f8f9fa;
      text-align: center;
      padding: 20px;
    }
    
    .error-icon {
      font-size: 80px;
      margin-bottom: 20px;
    }
    
    .error-title {
      color: #e74c3c;
      font-size: 32px;
      margin-bottom: 10px;
    }
    
    .error-message {
      color: #666;
      font-size: 18px;
      margin-bottom: 30px;
      max-width: 500px;
    }
    
    .error-actions {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 5px;
      text-decoration: none;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .btn-primary {
      background-color: #018820;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #016a1a;
    }
    
    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background-color: #545b62;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="error-icon">⚠️</div>
    <h1 class="error-title">Oops! Algo deu errado</h1>
    <p class="error-message">
      <% if (typeof error !== 'undefined') { %>
        <%= error %>
      <% } else { %>
        Ocorreu um erro inesperado. Por favor, tente novamente.
      <% } %>
    </p>
    
    <div class="error-actions">
      <a href="/dashboard" class="btn btn-primary">Voltar ao Dashboard</a>
      <a href="/login" class="btn btn-secondary">Fazer Login</a>
      <button onclick="history.back()" class="btn btn-secondary">Voltar</button>
    </div>
  </div>
</body>
</html>
