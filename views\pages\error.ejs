<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe.css">
</head>
<body>
  <div style="min-height: 100vh; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div style="text-align: center; max-width: 500px; padding: 40px;">
      <div style="font-size: 120px; margin-bottom: 20px;">😵</div>
      <h1 style="font-size: 48px; color: #018820; margin-bottom: 16px;">Oops!</h1>
      <h2 style="font-size: 24px; color: #333; margin-bottom: 20px;">Algo deu errado</h2>
      
      <% if (typeof error !== 'undefined') { %>
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
          <strong>Erro:</strong> <%= error %>
        </div>
      <% } %>
      
      <p style="color: #666; margin-bottom: 30px; line-height: 1.6;">
        Desculpe, ocorreu um erro inesperado. Nossa equipe foi notificada e está trabalhando para resolver o problema.
      </p>
      
      <div style="display: flex; gap: 15px; justify-content: center;">
        <a href="/dashboard" class="btn btn-primary">
          🏠 Voltar ao Dashboard
        </a>
        <button class="btn btn-secondary" onclick="window.location.reload()">
          🔄 Tentar Novamente
        </button>
      </div>
      
      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
        <p style="color: #999; font-size: 14px;">
          Se o problema persistir, entre em contato com o suporte técnico.
        </p>
      </div>
    </div>
  </div>
</body>
</html>
