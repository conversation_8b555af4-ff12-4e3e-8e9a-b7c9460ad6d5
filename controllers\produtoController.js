// controllers/produtoController.js

const Produto = require('../models/produtoModel');

const renderProdutos = async (req, res) => {
  try {
    const produtos = await Produto.getAll();
    res.render('pages/produtos', {
      pageTitle: 'Produtos - PCR Labor',
      currentPage: 'produtos',
      produtos
    });
  } catch (error) {
    console.error('Erro ao carregar produtos:', error);
    res.status(500).render('pages/error', {
      pageTitle: 'Erro - PCR Labor',
      error: 'Erro ao carregar produtos'
    });
  }
};

const renderEstoque = async (req, res) => {
  try {
    const [produtos, produtosEstoqueBaixo] = await Promise.all([
      Produto.getAll(),
      Produto.getEstoqueBaixo(10)
    ]);

    res.render('pages/estoque', {
      pageTitle: 'Estoque - PCR Labor',
      currentPage: 'estoque',
      produtos,
      produtosEstoqueBaixo
    });
  } catch (error) {
    console.error('Erro ao carregar estoque:', error);
    res.status(500).render('pages/error', {
      pageTitle: 'Erro - PCR Labor',
      error: 'Erro ao carregar estoque'
    });
  }
};

// API endpoints
const getAllProdutos = async (req, res) => {
  try {
    const produtos = await Produto.getAll();
    res.status(200).json({
      success: true,
      data: produtos,
      message: 'Produtos recuperados com sucesso'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const getProdutoById = async (req, res) => {
  try {
    const produto = await Produto.getById(req.params.id);
    if (produto) {
      res.status(200).json({
        success: true,
        data: produto,
        message: 'Produto encontrado'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Produto não encontrado'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const createProduto = async (req, res) => {
  try {
    const { id_empresa, nome, sku, preco, estoque_atual } = req.body;
    const newProduto = await Produto.create({ id_empresa, nome, sku, preco, estoque_atual });
    res.status(201).json({
      success: true,
      data: newProduto,
      message: 'Produto criado com sucesso'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const updateProduto = async (req, res) => {
  try {
    const { nome, sku, preco, estoque_atual } = req.body;
    const updatedProduto = await Produto.update(req.params.id, { nome, sku, preco, estoque_atual });
    if (updatedProduto) {
      res.status(200).json({
        success: true,
        data: updatedProduto,
        message: 'Produto atualizado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Produto não encontrado'
      });
    }
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const deleteProduto = async (req, res) => {
  try {
    const deleted = await Produto.delete(req.params.id);
    if (deleted) {
      res.status(200).json({
        success: true,
        message: 'Produto deletado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Produto não encontrado'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const updateEstoque = async (req, res) => {
  try {
    const { estoque_atual } = req.body;
    const updatedProduto = await Produto.updateEstoque(req.params.id, estoque_atual);
    if (updatedProduto) {
      res.status(200).json({
        success: true,
        data: updatedProduto,
        message: 'Estoque atualizado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Produto não encontrado'
      });
    }
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

module.exports = {
  renderProdutos,
  renderEstoque,
  getAllProdutos,
  getProdutoById,
  createProduto,
  updateProduto,
  deleteProduto,
  updateEstoque
};
