// routes/vendaRoutes.js

const express = require('express');
const router = express.Router();
const controladorVendas = require('../controllers/controladorVendas');
const controladorPedidos = require('../controllers/controladorPedidos');
const controladorUsuarios = require('../controllers/controladorUsuarios');

// GET /api/vendas - Buscar todas as vendas
router.get('/', controladorVendas.getAllVendas);

// GET /api/vendas/periodo - Buscar vendas por período
router.get('/periodo', controladorVendas.getVendasPorPeriodo);

// GET /api/vendas/relatorio - Relatório de vendas
router.get('/relatorio', controladorVendas.getRelatorioVendas);

// GET /api/vendas/:id - Buscar venda por ID
router.get('/:id', controladorVendas.getVendaById);

// POST /api/vendas - Criar nova venda
router.post('/', controladorVendas.createVenda);

// PUT /api/vendas/:id - Atualizar venda
router.put('/:id', controladorVendas.updateVenda);

// DELETE /api/vendas/:id - Deletar venda
router.delete('/:id', controladorVendas.deleteVenda);

// Rotas de renderização removidas - agora estão em pageRoutes.js

// Rotas API para pedidos
router.get('/api/pedidos', controladorPedidos.getAllPedidos);
router.get('/api/pedidos/:id', controladorPedidos.getPedidoById);
router.post('/api/pedidos', controladorPedidos.createPedido);
router.put('/api/pedidos/:id', controladorPedidos.updatePedido);
router.delete('/api/pedidos/:id', controladorPedidos.deletePedido);
router.get('/api/pedidos/status/resumo', controladorPedidos.getPedidosPorStatus);
router.get('/api/pedidos/projecao/compras', controladorPedidos.getProjecaoCompras);
router.get('/api/pedidos/relatorio/mensal', controladorPedidos.getRelatorioPedidos);

// Rotas API para usuários
router.get('/api/usuarios', controladorUsuarios.getAllUsuarios);
router.get('/api/usuarios/:id', controladorUsuarios.getUsuarioById);
router.post('/api/usuarios', controladorUsuarios.createUsuario);
router.put('/api/usuarios/:id', controladorUsuarios.updateUsuario);
router.put('/api/usuarios/:id/senha', controladorUsuarios.updatePassword);
router.delete('/api/usuarios/:id', controladorUsuarios.deleteUsuario);

// Rotas API para métodos de pagamento
router.get('/api/usuarios/:id/pagamentos', controladorUsuarios.getMetodosPagamento);
router.post('/api/usuarios/:id/pagamentos', controladorUsuarios.addMetodoPagamento);
router.put('/api/usuarios/:id/pagamentos/:metodoId', controladorUsuarios.updateMetodoPagamento);
router.delete('/api/usuarios/:id/pagamentos/:metodoId', controladorUsuarios.deleteMetodoPagamento);

module.exports = router;
