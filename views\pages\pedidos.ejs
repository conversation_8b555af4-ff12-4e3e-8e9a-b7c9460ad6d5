<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    .chart-container {
      position: relative;
      height: 300px;
      margin: 20px 0;
    }
    
    .priority-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }
    
    .priority-urgente {
      background: #fee2e2;
      color: #dc2626;
    }
    
    .priority-medio {
      background: #fef3c7;
      color: #d97706;
    }
    
    .priority-baixo {
      background: #dcfce7;
      color: #16a34a;
    }
    
    .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }
    
    .status-pendente {
      background: #fef3c7;
      color: #d97706;
    }
    
    .status-aprovado {
      background: #dbeafe;
      color: #2563eb;
    }
    
    .status-entregue {
      background: #dcfce7;
      color: #16a34a;
    }
    
    .status-cancelado {
      background: #fee2e2;
      color: #dc2626;
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
    
    .btn-small {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="app-layout">
    <%- include('../components/sidebar') %>
    
    <div class="main-content">
      <%- include('../components/topbar') %>
      
      <div class="content-area">
        <!-- Cards de Métricas -->
        <div class="cards-grid">
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📋</div>
              <h3 class="card-title">Total de Pedidos</h3>
            </div>
            <div class="card-value"><%= stats.totalPedidos %></div>
            <div class="card-subtitle">pedidos registrados</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">⏳</div>
              <h3 class="card-title">Pendentes</h3>
            </div>
            <div class="card-value"><%= stats.pedidosPendentes %></div>
            <div class="card-subtitle">aguardando aprovação</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">✅</div>
              <h3 class="card-title">Aprovados</h3>
            </div>
            <div class="card-value"><%= stats.pedidosAprovados %></div>
            <div class="card-subtitle">em processamento</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">💰</div>
              <h3 class="card-title">Valor Total</h3>
            </div>
            <div class="card-value">R$ <%= stats.valorTotalPedidos.toFixed(2) %></div>
            <div class="card-subtitle">em pedidos</div>
          </div>
        </div>
        
        <!-- Gráficos -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
          <!-- Gráfico de Pedidos por Status -->
          <div class="table-section">
            <div class="table-header">
              <h3 class="table-title">Pedidos por Status</h3>
            </div>
            <div class="chart-container">
              <canvas id="pedidosStatusChart"></canvas>
            </div>
          </div>
          
          <!-- Gráfico de Produtos Urgentes -->
          <div class="table-section">
            <div class="table-header">
              <h3 class="table-title">Prioridade de Reposição</h3>
            </div>
            <div class="chart-container">
              <canvas id="prioridadeChart"></canvas>
            </div>
          </div>
        </div>
        
        <!-- Tabela de Projeção de Compras -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Sugestões de Compra Automática</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Produto</th>
                <th>SKU</th>
                <th>Estoque Atual</th>
                <th>Média de Vendas</th>
                <th>Prioridade</th>
                <th>Sugestão</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (projecaoCompras && projecaoCompras.length > 0) { %>
                <% projecaoCompras.forEach(item => { %>
                  <tr>
                    <td><%= item.produto_nome %></td>
                    <td><%= item.sku %></td>
                    <td><%= item.estoque_atual %></td>
                    <td><%= parseFloat(item.media_pedidos || 0).toFixed(1) %>/mês</td>
                    <td>
                      <span class="priority-badge priority-<%= item.prioridade.toLowerCase() %>">
                        <%= item.prioridade %>
                      </span>
                    </td>
                    <td>
                      <% 
                        const sugestao = Math.max(50, item.estoque_atual + Math.ceil(item.media_pedidos || 0) + 20);
                      %>
                      <%= sugestao %> unidades
                    </td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-primary btn-small" onclick="criarPedido('<%= item.sku %>', <%= sugestao %>)">
                          Criar Pedido
                        </button>
                      </div>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="7" style="text-align: center; color: #666; padding: 40px;">
                    Nenhuma sugestão de compra disponível
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
        
        <!-- Tabela de Pedidos -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Histórico de Pedidos</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Fornecedor</th>
                <th>Quantidade</th>
                <th>Valor</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (pedidos && pedidos.length > 0) { %>
                <% pedidos.forEach(pedido => { %>
                  <tr>
                    <td><%= new Date(pedido.data_pedido).toLocaleDateString('pt-BR') %></td>
                    <td><%= pedido.produto_nome || 'N/A' %></td>
                    <td><%= pedido.plataforma_nome || 'N/A' %></td>
                    <td><%= pedido.quantidade %></td>
                    <td>R$ <%= parseFloat(pedido.valor_total || 0).toFixed(2) %></td>
                    <td>
                      <span class="status-badge status-<%= pedido.status %>">
                        <%= pedido.status %>
                      </span>
                    </td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-secondary btn-small" onclick="editarPedido(<%= pedido.id_pedido %>)">
                          Editar
                        </button>
                        <% if (pedido.status === 'pendente') { %>
                          <button class="btn btn-primary btn-small" onclick="aprovarPedido(<%= pedido.id_pedido %>)">
                            Aprovar
                          </button>
                        <% } %>
                      </div>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="7" style="text-align: center; color: #666; padding: 40px;">
                    Nenhum pedido encontrado
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Dados para os gráficos
    const pedidosStatusData = <%- JSON.stringify(pedidosPorStatus) %>;
    const projecaoData = <%- JSON.stringify(projecaoCompras) %>;

    // Gráfico de Pedidos por Status
    const statusCtx = document.getElementById('pedidosStatusChart').getContext('2d');
    new Chart(statusCtx, {
      type: 'doughnut',
      data: {
        labels: pedidosStatusData.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1)),
        datasets: [{
          data: pedidosStatusData.map(item => item.total_pedidos),
          backgroundColor: ['#fbbf24', '#3b82f6', '#10b981', '#ef4444'],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });

    // Gráfico de Prioridade
    const prioridadeCtx = document.getElementById('prioridadeChart').getContext('2d');
    const prioridadeCounts = {
      'URGENTE': projecaoData.filter(p => p.prioridade === 'URGENTE').length,
      'MEDIO': projecaoData.filter(p => p.prioridade === 'MEDIO').length,
      'BAIXO': projecaoData.filter(p => p.prioridade === 'BAIXO').length
    };

    new Chart(prioridadeCtx, {
      type: 'bar',
      data: {
        labels: ['Urgente', 'Médio', 'Baixo'],
        datasets: [{
          label: 'Produtos',
          data: [prioridadeCounts.URGENTE, prioridadeCounts.MEDIO, prioridadeCounts.BAIXO],
          backgroundColor: ['#dc2626', '#d97706', '#16a34a'],
          borderRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        }
      }
    });

    // Funções JavaScript
    function criarPedido(sku, quantidade) {
      alert(`Criando pedido para ${sku}: ${quantidade} unidades`);
      // Aqui implementaria a lógica real de criação de pedido
    }

    function editarPedido(id) {
      alert(`Editando pedido ID: ${id}`);
      // Aqui implementaria a lógica real de edição
    }

    function aprovarPedido(id) {
      if (confirm('Deseja aprovar este pedido?')) {
        alert(`Pedido ${id} aprovado!`);
        // Aqui implementaria a lógica real de aprovação
      }
    }
  </script>
</body>
</html>
