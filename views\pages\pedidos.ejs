<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-final.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- Header horizontal (Topo) com logo e menu -->
  <%- include('../components/header-horizontal') %>
  
  <!-- Layout principal -->
  <div class="main-layout">
    <!-- Área central para o conteúdo principal -->
    <main class="content-area">
      <!-- 6️⃣ Tela de Pedidos -->
      <h1 class="page-title">Pedidos</h1>
      
      <!-- Botões de filtro: "Este mês", "Quantidade $" -->
      <div style="display: flex; gap: 15px; margin-bottom: 30px;">
        <button class="btn btn-primary">Este mês</button>
        <button class="btn btn-secondary">Quantidade $</button>
        <button class="btn btn-secondary">Últimos 7 dias</button>
        <button class="btn btn-secondary">+ Novo Pedido</button>
      </div>
      
      <!-- Projeção de compra: Gráfico de linha -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📈 Projeção de Compra</h3>
        </div>
        <div class="section-content">
          <div class="chart-container">
            <canvas id="projecaoChart"></canvas>
          </div>
        </div>
      </div>
      
      <!-- Check-out de pedidos: Lista de pedidos com status -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📋 Check-out de Pedidos</h3>
          <button class="btn btn-primary">+ Criar Pedido</button>
        </div>
        <div class="section-content">
          <% if (pedidos && pedidos.length > 0) { %>
            <div style="display: grid; gap: 15px;">
              <% pedidos.forEach((pedido, index) => { %>
                <div style="background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 12px; padding: 20px; transition: all 0.2s ease;">
                  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                      <div style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; font-weight: bold;">
                        #<%= index + 1 %>
                      </div>
                      <div>
                        <h4 style="margin: 0; color: #333; font-size: 16px;"><%= pedido.produto_nome || 'Produto N/A' %></h4>
                        <p style="margin: 0; color: #666; font-size: 14px;">
                          <%= new Date(pedido.data_pedido).toLocaleDateString('pt-BR') %> • 
                          Qtd: <%= pedido.quantidade %>
                        </p>
                      </div>
                    </div>
                    <div style="text-align: right;">
                      <div style="font-size: 18px; font-weight: bold; color: #3b82f6; margin-bottom: 5px;">
                        R$ <%= parseFloat(pedido.valor_total || 0).toFixed(2) %>
                      </div>
                      <div style="display: flex; align-items: center; gap: 10px;">
                        <% if (pedido.status === 'PENDENTE') { %>
                          <span style="background: #fef3c7; color: #d97706; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                            ⏳ Pendente
                          </span>
                        <% } else if (pedido.status === 'APROVADO') { %>
                          <span style="background: #dcfce7; color: #16a34a; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                            ✅ Aprovado
                          </span>
                        <% } else { %>
                          <span style="background: #fee2e2; color: #dc2626; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                            ❌ Cancelado
                          </span>
                        <% } %>
                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">
                          👁️ Ver
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
                    <div>
                      <label style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Fornecedor</label>
                      <div style="font-size: 14px; font-weight: bold; color: #333;"><%= pedido.fornecedor || 'PCR Labor' %></div>
                    </div>
                    <div>
                      <label style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Prazo</label>
                      <div style="font-size: 14px; font-weight: bold; color: #333;">5-7 dias úteis</div>
                    </div>
                    <div>
                      <label style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Prioridade</label>
                      <div style="font-size: 14px; font-weight: bold; color: #f59e0b;">MÉDIA</div>
                    </div>
                    <div>
                      <label style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Ações</label>
                      <div style="display: flex; gap: 5px;">
                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 11px;">✅ Aprovar</button>
                        <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 11px;">❌ Cancelar</button>
                      </div>
                    </div>
                  </div>
                </div>
              <% }) %>
            </div>
          <% } else { %>
            <div style="text-align: center; color: #666; padding: 60px 20px;">
              <div style="font-size: 64px; margin-bottom: 20px;">📋</div>
              <h3 style="margin: 0 0 10px; color: #333;">Nenhum pedido encontrado</h3>
              <p style="margin: 0 0 30px;">Seus pedidos aparecerão aqui quando criados</p>
              <button class="btn btn-primary">
                + Criar Primeiro Pedido
              </button>
            </div>
          <% } %>
        </div>
      </div>
      
    </main>
    
    <!-- Lateral direita com Tarefas, Calendário e Pergunte para IA -->
    <%- include('../components/sidebar-right') %>
  </div>

  <script>
    // Gráfico de projeção de compra
    const projecaoCtx = document.getElementById('projecaoChart').getContext('2d');
    
    new Chart(projecaoCtx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul'],
        datasets: [{
          label: 'Projeção de Compras (R$)',
          data: [8000, 12000, 6000, 15000, 18000, 14000, 20000],
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  </script>
</body>
</html>
