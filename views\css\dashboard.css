/* Reset e Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

/* Layout Principal */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  border-right: 1px solid #e2e8f0;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 32px 24px;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
  background: #ffffff;
}

.sidebar-logo {
  width: 140px;
  height: 140px;
  margin-bottom: 16px;
  object-fit: contain;
  border-radius: 8px;
}

.sidebar-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: #018820;
  margin: 0;
  letter-spacing: -0.025em;
}

/* Navegação da Sidebar */
.sidebar-nav {
  padding: 24px 0;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin: 4px 16px;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #64748b;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  position: relative;
}

.sidebar-nav a:hover {
  background-color: #f1f5f9;
  color: #018820;
  transform: translateX(2px);
}

.sidebar-nav a.active {
  background-color: #018820;
  color: white;
  box-shadow: 0 2px 4px rgba(1, 136, 32, 0.2);
}

.sidebar-nav a.active::before {
  content: '';
  position: absolute;
  left: -16px;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #018820;
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  margin-right: 12px;
  font-size: 20px;
  width: 24px;
  text-align: center;
  opacity: 0.8;
}

.nav-text {
  font-weight: 500;
}

/* Footer da Sidebar */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24px;
  border-top: 1px solid #e2e8f0;
  background: #ffffff;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  margin-bottom: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #018820, #02a025);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
  font-weight: 600;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  color: #1e293b;
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: #64748b;
}

.logout-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #dc2626;
  text-decoration: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #fecaca;
}

.logout-btn:hover {
  background-color: #dc2626;
  color: white;
  border-color: #dc2626;
}

/* Conteúdo Principal */
.main-content {
  flex: 1;
  margin-left: 280px;
  min-height: 100vh;
  background-color: #f8fafc;
}

/* Topbar */
.topbar {
  background: #ffffff;
  padding: 16px 32px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 999;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.topbar-left {
  display: flex;
  align-items: center;
}

.breadcrumb {
  display: flex;
  align-items: center;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.separator {
  margin: 0 8px;
  color: #cbd5e1;
}

.current-page {
  color: #018820;
  font-weight: 600;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.search-box:focus-within {
  border-color: #018820;
  box-shadow: 0 0 0 3px rgba(1, 136, 32, 0.1);
}

.search-box input {
  border: none;
  background: none;
  outline: none;
  padding: 0;
  width: 240px;
  font-size: 14px;
  color: #1e293b;
}

.search-box input::placeholder {
  color: #94a3b8;
}

.search-box button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #64748b;
  padding: 4px;
  margin-left: 8px;
}

/* Dashboard Container */
.dashboard-container {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.header-text h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
  letter-spacing: -0.025em;
}

.header-text p {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 12px;
}

.last-update {
  font-size: 14px;
  color: #94a3b8;
  font-style: italic;
}

.warning-badge {
  display: inline-block;
  background-color: #fef3c7;
  color: #92400e;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
  border: 1px solid #fde68a;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Botões */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #018820;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #016a1a;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(1, 136, 32, 0.3);
}

.btn-secondary {
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e2e8f0;
  color: #334155;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Grid de Estatísticas */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* Cards de Estatísticas */
.stat-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #018820, #02a025);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card.primary::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card.warning::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.stat-card.success::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.stat-card.info::before {
  background: linear-gradient(90deg, #06b6d4, #0891b2);
}

.stat-card.secondary::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.stat-card.accent::before {
  background: linear-gradient(90deg, #f97316, #ea580c);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stat-details {
  flex: 1;
}

.stat-details h3 {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-details p {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 6px;
}

.stat-trend {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 400;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* Dashboard Cards */
.dashboard-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dashboard-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Gráfico de Barras */
.chart-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.platform-name {
  min-width: 120px;
  font-size: 14px;
  font-weight: 500;
  color: #475569;
}

.chart-bar {
  flex: 1;
  height: 8px;
  background-color: #f1f5f9;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #018820, #02a025);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.platform-value {
  min-width: 80px;
  text-align: right;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

/* Tabelas */
.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th {
  background-color: #f8fafc;
  color: #475569;
  font-weight: 600;
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table td {
  padding: 12px;
  border-bottom: 1px solid #f1f5f9;
  color: #1e293b;
}

.data-table tr:hover {
  background-color: #f8fafc;
}

.data-table .status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status.low-stock {
  background-color: #fef3c7;
  color: #92400e;
}

.status.in-stock {
  background-color: #d1fae5;
  color: #065f46;
}

/* Sugestões de Reposição */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.suggestion-info {
  flex: 1;
}

.suggestion-info strong {
  display: block;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 4px;
}

.suggestion-info .sku {
  font-size: 12px;
  color: #64748b;
  background-color: #e2e8f0;
  padding: 2px 6px;
  border-radius: 4px;
  margin-bottom: 8px;
  display: inline-block;
}

.suggestion-info p {
  font-size: 14px;
  color: #475569;
  margin: 0;
}

.suggestion-action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.current-stock,
.suggested-stock {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.current-stock {
  background-color: #fef3c7;
  color: #92400e;
}

.suggested-stock {
  background-color: #d1fae5;
  color: #065f46;
}
  right: -5px;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-dropdown,
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  min-width: 250px;
  display: none;
  z-index: 1000;
}

.notification-dropdown.show,
.user-dropdown.show {
  display: block;
}

.notification-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.notification-header h4 {
  margin: 0;
  color: #333;
}

.notification-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.notification-icon {
  margin-right: 10px;
  font-size: 16px;
}

.notification-content p {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.notification-content small {
  color: #666;
  font-size: 12px;
}

.user-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  gap: 10px;
}

.dropdown-item {
  display: block;
  padding: 10px 15px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-divider {
  margin: 5px 0;
  border: none;
  border-top: 1px solid #eee;
}

/* Dashboard Content */
.dashboard-container {
  padding: 30px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 28px;
}

.dashboard-header p {
  margin: 5px 0 0 0;
  color: #666;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 40px;
  margin-right: 20px;
}

.stat-info h3 {
  margin: 0;
  font-size: 32px;
  color: #018820;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.dashboard-card {
  background-color: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
}

/* Chart Container */
.chart-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.platform-name {
  min-width: 100px;
  font-size: 14px;
  color: #333;
}

.chart-bar {
  flex: 1;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #018820, #02a025);
  transition: width 0.3s ease;
}

.platform-value {
  min-width: 80px;
  text-align: right;
  font-weight: bold;
  color: #018820;
}

/* Table Container */
.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table th,
table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #333;
}

table tr:hover {
  background-color: #f8f9fa;
}

.low-stock {
  color: #e74c3c;
  font-weight: bold;
}

/* Financial Summary */
.financial-summary {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.financial-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.financial-item .label {
  color: #666;
  font-size: 14px;
}

.financial-item .value {
  font-weight: bold;
  color: #018820;
  font-size: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 60px;
  }

  .main-content {
    margin-left: 60px;
    width: calc(100% - 60px);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .topbar {
    padding: 15px;
  }

  .search-box input {
    width: 150px;
  }
}
