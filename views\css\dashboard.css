/* Dashboard Layout */
body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  display: flex;
}

/* Sidebar */
.sidebar {
  width: 250px;
  background-color: #2c3e50;
  color: white;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  overflow-y: auto;
  transition: width 0.3s ease;
  z-index: 1000;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #34495e;
}

.sidebar-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  color: #018820;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.sidebar-nav li {
  margin: 5px 0;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s ease;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
  background-color: #018820;
  color: white;
}

.nav-icon {
  margin-right: 15px;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.sidebar-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 20px;
  border-top: 1px solid #34495e;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background-color: #018820;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: bold;
  font-size: 14px;
}

.user-role {
  font-size: 12px;
  color: #bdc3c7;
}

.logout-btn {
  display: flex;
  align-items: center;
  padding: 10px;
  color: #e74c3c;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.logout-btn:hover {
  background-color: #e74c3c;
  color: white;
}

/* Main Content */
.main-content {
  margin-left: 250px;
  width: calc(100% - 250px);
  min-height: 100vh;
  transition: margin-left 0.3s ease, width 0.3s ease;
}

.sidebar.collapsed + .main-content {
  margin-left: 60px;
  width: calc(100% - 60px);
}

/* Topbar */
.topbar {
  background-color: white;
  padding: 15px 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 999;
}

.topbar-left {
  display: flex;
  align-items: center;
}

.menu-toggle {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  margin-right: 20px;
  padding: 5px;
}

.breadcrumb {
  color: #666;
  font-size: 14px;
}

.separator {
  margin: 0 10px;
}

.current-page {
  color: #018820;
  font-weight: bold;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 20px;
  padding: 5px 15px;
}

.search-box input {
  border: none;
  background: none;
  outline: none;
  padding: 5px;
  width: 200px;
}

.search-box button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.notifications {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-dropdown,
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  min-width: 250px;
  display: none;
  z-index: 1000;
}

.notification-dropdown.show,
.user-dropdown.show {
  display: block;
}

.notification-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.notification-header h4 {
  margin: 0;
  color: #333;
}

.notification-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.notification-icon {
  margin-right: 10px;
  font-size: 16px;
}

.notification-content p {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.notification-content small {
  color: #666;
  font-size: 12px;
}

.user-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  gap: 10px;
}

.dropdown-item {
  display: block;
  padding: 10px 15px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-divider {
  margin: 5px 0;
  border: none;
  border-top: 1px solid #eee;
}

/* Dashboard Content */
.dashboard-container {
  padding: 30px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 28px;
}

.dashboard-header p {
  margin: 5px 0 0 0;
  color: #666;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 40px;
  margin-right: 20px;
}

.stat-info h3 {
  margin: 0;
  font-size: 32px;
  color: #018820;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.dashboard-card {
  background-color: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
}

/* Chart Container */
.chart-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.platform-name {
  min-width: 100px;
  font-size: 14px;
  color: #333;
}

.chart-bar {
  flex: 1;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #018820, #02a025);
  transition: width 0.3s ease;
}

.platform-value {
  min-width: 80px;
  text-align: right;
  font-weight: bold;
  color: #018820;
}

/* Table Container */
.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table th,
table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #333;
}

table tr:hover {
  background-color: #f8f9fa;
}

.low-stock {
  color: #e74c3c;
  font-weight: bold;
}

/* Financial Summary */
.financial-summary {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.financial-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.financial-item .label {
  color: #666;
  font-size: 14px;
}

.financial-item .value {
  font-weight: bold;
  color: #018820;
  font-size: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 60px;
  }

  .main-content {
    margin-left: 60px;
    width: calc(100% - 60px);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .topbar {
    padding: 15px;
  }

  .search-box input {
    width: 150px;
  }
}
