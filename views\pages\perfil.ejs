<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/estilos.css">
</head>
<body>
  <!-- Header horizontal (Topo) com logo e menu -->
  <%- include('../components/cabecalho') %>

  <!-- Layout principal -->
  <div class="main-layout">
    <!-- Área central para o conteúdo principal -->
    <main class="content-area">
      <!-- 8️⃣ Tela de Perfil -->
      <h1 class="page-title">Perfil</h1>

      <!-- <PERSON><PERSON><PERSON> de mês (ex.: "Este mês") -->
      <div style="display: flex; gap: 15px; margin-bottom: 30px;">
        <button class="btn btn-primary">Este mês</button>
        <button class="btn btn-secondary">Último mês</button>
        <button class="btn btn-secondary">Últimos 3 meses</button>
      </div>

      <!-- <PERSON><PERSON> principais: "A pagar", "A receber", "Saldo" -->
      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 40px;">
        <div class="summary-card">
          <div class="card-icon" style="background: #ef4444;">💸</div>
          <div class="card-title">A pagar</div>
          <div class="card-value">R$ 12.450</div>
        </div>

        <div class="summary-card">
          <div class="card-icon" style="background: #16a34a;">💰</div>
          <div class="card-title">A receber</div>
          <div class="card-value">R$ 28.900</div>
        </div>

        <div class="summary-card">
          <div class="card-icon" style="background: #3b82f6;">💳</div>
          <div class="card-title">Saldo</div>
          <div class="card-value">R$ 16.450</div>
        </div>
      </div>

      <!-- Blocos de meios de pagamento -->
      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin-bottom: 40px;">

        <!-- Pagamento via boleto -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🧾 Pagamento via boleto</h3>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 15px;">
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">Banco do Brasil</div>
                <div style="color: #666; font-size: 14px;">Agência: 1234-5</div>
                <div style="color: #666; font-size: 14px;">Conta: 67890-1</div>
              </div>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">Itaú</div>
                <div style="color: #666; font-size: 14px;">Agência: 5678-9</div>
                <div style="color: #666; font-size: 14px;">Conta: 12345-6</div>
              </div>
              <button class="btn btn-primary" style="width: 100%;">+ Adicionar Conta</button>
            </div>
          </div>
        </div>

        <!-- Transferência bancária -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🏦 Transferência bancária</h3>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 15px;">
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">PIX</div>
                <div style="color: #666; font-size: 14px;"><EMAIL></div>
                <div style="color: #018820; font-size: 12px; font-weight: bold;">✅ Verificado</div>
              </div>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">TED/DOC</div>
                <div style="color: #666; font-size: 14px;">Banco: 001 - Banco do Brasil</div>
                <div style="color: #666; font-size: 14px;">Ag: 1234-5 | CC: 67890-1</div>
              </div>
              <button class="btn btn-primary" style="width: 100%;">+ Adicionar Método</button>
            </div>
          </div>
        </div>

        <!-- Link de pagamento -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🔗 Link de pagamento</h3>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 15px;">
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">Mercado Pago</div>
                <div style="color: #666; font-size: 14px;">Taxa: 4,99%</div>
                <div style="color: #018820; font-size: 12px; font-weight: bold;">✅ Ativo</div>
              </div>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">PagSeguro</div>
                <div style="color: #666; font-size: 14px;">Taxa: 3,99%</div>
                <div style="color: #f59e0b; font-size: 12px; font-weight: bold;">⏳ Pendente</div>
              </div>
              <button class="btn btn-primary" style="width: 100%;">+ Novo Link</button>
            </div>
          </div>
        </div>

      </div>

      <!-- Informações do usuário -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">👤 Informações Pessoais</h3>
          <button class="btn btn-secondary">✏️ Editar</button>
        </div>
        <div class="section-content">
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px;">
            <div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Nome Completo</label>
                <div style="font-size: 16px; color: #333; font-weight: bold;"><%= usuario.nome %></div>
              </div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Email</label>
                <div style="font-size: 16px; color: #333;"><%= usuario.email %></div>
              </div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Telefone</label>
                <div style="font-size: 16px; color: #333;"><%= usuario.telefone || '(11) 99999-9999' %></div>
              </div>
            </div>
            <div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">CPF/CNPJ</label>
                <div style="font-size: 16px; color: #333;"><%= usuario.documento || '123.456.789-00' %></div>
              </div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Último Login</label>
                <div style="font-size: 16px; color: #333;"><%= usuario.ultimoLogin %></div>
              </div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Status</label>
                <div style="font-size: 16px; color: #16a34a; font-weight: bold;">✅ Ativo</div>
              </div>
            </div>
          </div>

          <div style="display: flex; gap: 15px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
            <button class="btn btn-primary">💾 Salvar Alterações</button>
            <button class="btn btn-secondary">🔒 Alterar Senha</button>
            <button class="btn btn-secondary">📄 Baixar Dados</button>
          </div>
        </div>
      </div>

    </main>

    <!-- Lateral direita com Tarefas, Calendário e Pergunte para IA -->
    <%- include('../components/barraLateral') %>
  </div>
</body>
</html>
