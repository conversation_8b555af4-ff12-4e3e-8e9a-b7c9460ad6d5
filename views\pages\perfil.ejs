<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
  <style>
    .profile-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 30px;
    }
    
    .profile-card {
      background: white;
      border-radius: 8px;
      padding: 30px;
      border: 1px solid #e0e0e0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .profile-header {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
    }
    
    .profile-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: #018820;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 32px;
      font-weight: bold;
      margin-right: 20px;
    }
    
    .profile-info h2 {
      margin: 0 0 5px;
      color: #333;
      font-size: 24px;
    }
    
    .profile-info p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 6px;
    }
    
    .form-input {
      width: 100%;
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }
    
    .form-input:focus {
      outline: none;
      border-color: #018820;
    }
    
    .payment-method {
      background: #f8f9fa;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 15px;
    }
    
    .payment-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .payment-type {
      font-weight: 600;
      color: #333;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .payment-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }
    
    .status-ativo {
      background: #dcfce7;
      color: #16a34a;
    }
    
    .status-inativo {
      background: #fee2e2;
      color: #dc2626;
    }
    
    .payment-details {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .payment-actions {
      margin-top: 15px;
      display: flex;
      gap: 10px;
    }
    
    .btn-small {
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 4px;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      margin-bottom: 20px;
    }
    
    .stat-item {
      text-align: center;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #018820;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
    }
  </style>
</head>
<body>
  <div class="app-layout">
    <%- include('../components/sidebar') %>
    
    <div class="main-content">
      <%- include('../components/topbar') %>
      
      <div class="content-area">
        <!-- Cards de Estatísticas do Usuário -->
        <div class="cards-grid">
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">👤</div>
              <h3 class="card-title">Tempo na Empresa</h3>
            </div>
            <div class="card-value"><%= stats.tempoNaEmpresa %></div>
            <div class="card-subtitle">dias</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">💳</div>
              <h3 class="card-title">Métodos Ativos</h3>
            </div>
            <div class="card-value"><%= stats.metodosPagamentoAtivos %></div>
            <div class="card-subtitle">métodos de pagamento</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">🕐</div>
              <h3 class="card-title">Último Login</h3>
            </div>
            <div class="card-value" style="font-size: 16px;"><%= stats.ultimoLogin %></div>
            <div class="card-subtitle">data do acesso</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">🟢</div>
              <h3 class="card-title">Status</h3>
            </div>
            <div class="card-value" style="font-size: 16px; color: #16a34a;">Online</div>
            <div class="card-subtitle">sessão ativa</div>
          </div>
        </div>
        
        <!-- Container Principal do Perfil -->
        <div class="profile-container">
          <!-- Informações Pessoais -->
          <div class="profile-card">
            <div class="profile-header">
              <div class="profile-avatar">
                <%= usuario.nome.charAt(0).toUpperCase() %>
              </div>
              <div class="profile-info">
                <h2><%= usuario.nome %></h2>
                <p><%= usuario.cargo %> • <%= usuario.empresa_nome %></p>
              </div>
            </div>
            
            <form id="profileForm">
              <div class="form-group">
                <label for="nome" class="form-label">Nome Completo</label>
                <input type="text" id="nome" name="nome" class="form-input" value="<%= usuario.nome %>">
              </div>
              
              <div class="form-group">
                <label for="email" class="form-label">Email</label>
                <input type="email" id="email" name="email" class="form-input" value="<%= usuario.email %>">
              </div>
              
              <div class="form-group">
                <label for="telefone" class="form-label">Telefone</label>
                <input type="tel" id="telefone" name="telefone" class="form-input" value="<%= usuario.telefone %>">
              </div>
              
              <div class="form-group">
                <label for="cargo" class="form-label">Cargo</label>
                <input type="text" id="cargo" name="cargo" class="form-input" value="<%= usuario.cargo %>">
              </div>
              
              <div style="display: flex; gap: 10px;">
                <button type="submit" class="btn btn-primary">
                  Salvar Alterações
                </button>
                <button type="button" class="btn btn-secondary" onclick="alterarSenha()">
                  Alterar Senha
                </button>
              </div>
            </form>
          </div>
          
          <!-- Informações da Empresa -->
          <div class="profile-card">
            <h3 style="margin-bottom: 20px; color: #333;">Informações da Empresa</h3>
            
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">PCR</div>
                <div class="stat-label">Empresa</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">2024</div>
                <div class="stat-label">Ano</div>
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">Razão Social</label>
              <input type="text" class="form-input" value="<%= usuario.empresa_nome %>" readonly>
            </div>
            
            <div class="form-group">
              <label class="form-label">CNPJ</label>
              <input type="text" class="form-input" value="<%= usuario.empresa_cnpj %>" readonly>
            </div>
            
            <div class="form-group">
              <label class="form-label">Setor</label>
              <input type="text" class="form-input" value="Biotecnologia e Diagnósticos" readonly>
            </div>
          </div>
        </div>
        
        <!-- Métodos de Pagamento -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Métodos de Pagamento</h3>
            <button class="btn btn-primary" onclick="adicionarMetodoPagamento()">
              + Adicionar Método
            </button>
          </div>
          
          <div style="padding: 20px;">
            <% if (metodosPagamento && metodosPagamento.length > 0) { %>
              <% metodosPagamento.forEach(metodo => { %>
                <div class="payment-method">
                  <div class="payment-header">
                    <div class="payment-type">
                      <% if (metodo.tipo === 'boleto') { %>
                        📄 <%= metodo.descricao %>
                      <% } else if (metodo.tipo === 'transferencia') { %>
                        🏦 <%= metodo.descricao %>
                      <% } else if (metodo.tipo === 'link') { %>
                        🔗 <%= metodo.descricao %>
                      <% } else { %>
                        💳 <%= metodo.descricao %>
                      <% } %>
                    </div>
                    <span class="payment-status status-<%= metodo.ativo ? 'ativo' : 'inativo' %>">
                      <%= metodo.ativo ? 'Ativo' : 'Inativo' %>
                    </span>
                  </div>
                  
                  <div class="payment-details">
                    <% if (metodo.tipo === 'boleto' && metodo.dados_pagamento.banco) { %>
                      <strong>Banco:</strong> <%= metodo.dados_pagamento.banco %><br>
                      <strong>Agência:</strong> <%= metodo.dados_pagamento.agencia %><br>
                      <strong>Conta:</strong> <%= metodo.dados_pagamento.conta %>
                    <% } else if (metodo.tipo === 'transferencia' && metodo.dados_pagamento.banco) { %>
                      <strong>Banco:</strong> <%= metodo.dados_pagamento.banco %><br>
                      <strong>Agência:</strong> <%= metodo.dados_pagamento.agencia %><br>
                      <strong>Conta:</strong> <%= metodo.dados_pagamento.conta %><br>
                      <% if (metodo.dados_pagamento.pix) { %>
                        <strong>PIX:</strong> <%= metodo.dados_pagamento.pix %>
                      <% } %>
                    <% } else if (metodo.tipo === 'link' && metodo.dados_pagamento.gateway) { %>
                      <strong>Gateway:</strong> <%= metodo.dados_pagamento.gateway %><br>
                      <strong>Link:</strong> <a href="<%= metodo.dados_pagamento.link %>" target="_blank">Acessar</a>
                    <% } %>
                  </div>
                  
                  <div class="payment-actions">
                    <button class="btn btn-secondary btn-small" onclick="editarMetodo(<%= metodo.id_metodo %>)">
                      Editar
                    </button>
                    <button class="btn btn-primary btn-small" onclick="toggleMetodo(<%= metodo.id_metodo %>, <%= !metodo.ativo %>)">
                      <%= metodo.ativo ? 'Desativar' : 'Ativar' %>
                    </button>
                    <button class="btn btn-secondary btn-small" onclick="removerMetodo(<%= metodo.id_metodo %>)" style="color: #dc2626;">
                      Remover
                    </button>
                  </div>
                </div>
              <% }) %>
            <% } else { %>
              <div style="text-align: center; color: #666; padding: 40px;">
                <p>Nenhum método de pagamento cadastrado</p>
                <button class="btn btn-primary" onclick="adicionarMetodoPagamento()">
                  Adicionar Primeiro Método
                </button>
              </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Funções do perfil
    document.getElementById('profileForm').addEventListener('submit', function(e) {
      e.preventDefault();
      alert('Perfil atualizado com sucesso!');
      // Aqui implementaria a lógica real de atualização
    });

    function alterarSenha() {
      const novaSenha = prompt('Digite a nova senha:');
      if (novaSenha) {
        alert('Senha alterada com sucesso!');
        // Aqui implementaria a lógica real de alteração de senha
      }
    }

    function adicionarMetodoPagamento() {
      alert('Abrindo formulário para adicionar método de pagamento');
      // Aqui implementaria um modal ou redirecionamento
    }

    function editarMetodo(id) {
      alert(`Editando método de pagamento ID: ${id}`);
      // Aqui implementaria a lógica real de edição
    }

    function toggleMetodo(id, ativar) {
      const acao = ativar ? 'ativar' : 'desativar';
      if (confirm(`Deseja ${acao} este método de pagamento?`)) {
        alert(`Método ${ativar ? 'ativado' : 'desativado'} com sucesso!`);
        // Aqui implementaria a lógica real de toggle
      }
    }

    function removerMetodo(id) {
      if (confirm('Deseja realmente remover este método de pagamento?')) {
        alert('Método removido com sucesso!');
        // Aqui implementaria a lógica real de remoção
      }
    }
  </script>
</body>
</html>
