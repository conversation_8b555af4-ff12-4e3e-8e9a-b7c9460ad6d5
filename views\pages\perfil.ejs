<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/estilos.css">
</head>
<body>
  <!-- Header horizontal (Topo) com logo e menu -->
  <%- include('../components/cabecalho') %>

  <!-- Layout principal -->
  <div class="main-layout">
    <!-- Área central para o conteúdo principal -->
    <main class="content-area">
      <!-- 8️⃣ Tela de Perfil -->
      <h1 class="page-title">Perfil</h1>

      <!-- Seletor de período financeiro -->
      <div style="display: flex; gap: 15px; margin-bottom: 30px;">
        <button class="btn btn-primary" onclick="filtrarPeriodoFinanceiro('mes')" id="filtroMesPerfil">Este mês</button>
        <button class="btn btn-secondary" onclick="filtrarPeriodoFinanceiro('ultimo')" id="filtroUltimoPerfil">Último mês</button>
        <button class="btn btn-secondary" onclick="filtrarPeriodoFinanceiro('trimestre')" id="filtroTrimestre">Últimos 3 meses</button>
        <button class="btn btn-secondary" onclick="abrirRelatorioFinanceiro()">📊 Relatório</button>
      </div>

      <!-- Campos principais: "A pagar", "A receber", "Saldo" -->
      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 40px;">
        <div class="summary-card">
          <div class="card-icon" style="background: #ef4444;">💸</div>
          <div class="card-title">A pagar</div>
          <div class="card-value">R$ 12.450</div>
        </div>

        <div class="summary-card">
          <div class="card-icon" style="background: #16a34a;">💰</div>
          <div class="card-title">A receber</div>
          <div class="card-value">R$ 28.900</div>
        </div>

        <div class="summary-card">
          <div class="card-icon" style="background: #3b82f6;">💳</div>
          <div class="card-title">Saldo</div>
          <div class="card-value">R$ 16.450</div>
        </div>
      </div>

      <!-- Blocos de meios de pagamento -->
      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin-bottom: 40px;">

        <!-- Pagamento via boleto -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🧾 Pagamento via boleto</h3>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 15px;">
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">Banco do Brasil</div>
                <div style="color: #666; font-size: 14px;">Agência: 1234-5</div>
                <div style="color: #666; font-size: 14px;">Conta: 67890-1</div>
              </div>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">Itaú</div>
                <div style="color: #666; font-size: 14px;">Agência: 5678-9</div>
                <div style="color: #666; font-size: 14px;">Conta: 12345-6</div>
              </div>
              <button class="btn btn-primary" onclick="adicionarContaBancaria()" style="width: 100%;">+ Adicionar Conta</button>
            </div>
          </div>
        </div>

        <!-- Transferência bancária -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🏦 Transferência bancária</h3>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 15px;">
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">PIX</div>
                <div style="color: #666; font-size: 14px;"><EMAIL></div>
                <div style="color: #018820; font-size: 12px; font-weight: bold;">✅ Verificado</div>
              </div>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">TED/DOC</div>
                <div style="color: #666; font-size: 14px;">Banco: 001 - Banco do Brasil</div>
                <div style="color: #666; font-size: 14px;">Ag: 1234-5 | CC: 67890-1</div>
              </div>
              <button class="btn btn-primary" onclick="adicionarMetodoTransferencia()" style="width: 100%;">+ Adicionar Método</button>
            </div>
          </div>
        </div>

        <!-- Link de pagamento -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🔗 Link de pagamento</h3>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 15px;">
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">Mercado Pago</div>
                <div style="color: #666; font-size: 14px;">Taxa: 4,99%</div>
                <div style="color: #018820; font-size: 12px; font-weight: bold;">✅ Ativo</div>
              </div>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">PagSeguro</div>
                <div style="color: #666; font-size: 14px;">Taxa: 3,99%</div>
                <div style="color: #f59e0b; font-size: 12px; font-weight: bold;">⏳ Pendente</div>
              </div>
              <button class="btn btn-primary" onclick="adicionarLinkPagamento()" style="width: 100%;">+ Novo Link</button>
            </div>
          </div>
        </div>

      </div>

      <!-- Informações do usuário -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">👤 Informações Pessoais</h3>
          <button class="btn btn-secondary" onclick="editarInformacoesPessoais()">✏️ Editar</button>
        </div>
        <div class="section-content">
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px;">
            <div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Nome Completo</label>
                <div style="font-size: 16px; color: #333; font-weight: bold;"><%= usuario.nome %></div>
              </div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Email</label>
                <div style="font-size: 16px; color: #333;"><%= usuario.email %></div>
              </div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Telefone</label>
                <div style="font-size: 16px; color: #333;"><%= usuario.telefone || '(11) 99999-9999' %></div>
              </div>
            </div>
            <div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">CPF/CNPJ</label>
                <div style="font-size: 16px; color: #333;"><%= usuario.documento || '123.456.789-00' %></div>
              </div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Último Login</label>
                <div style="font-size: 16px; color: #333;"><%= usuario.ultimoLogin %></div>
              </div>
              <div style="margin-bottom: 20px;">
                <label style="font-size: 14px; color: #666; font-weight: 600;">Status</label>
                <div style="font-size: 16px; color: #16a34a; font-weight: bold;">✅ Ativo</div>
              </div>
            </div>
          </div>

          <div style="display: flex; gap: 15px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
            <button class="btn btn-primary" onclick="salvarAlteracoes()">💾 Salvar Alterações</button>
            <button class="btn btn-secondary" onclick="alterarSenha()">🔒 Alterar Senha</button>
            <button class="btn btn-secondary" onclick="baixarDados()">📄 Baixar Dados</button>
          </div>
        </div>
      </div>

    </main>

    <!-- Lateral direita com Tarefas, Calendário e Pergunte para IA -->
    <%- include('../components/barraLateral') %>
  </div>

  <script>
    // ===== FUNCIONALIDADES DE PERFIL =====

    // Filtros de período financeiro
    function filtrarPeriodoFinanceiro(periodo) {
      // Atualizar botões visuais
      document.querySelectorAll('[id*="filtro"]').forEach(btn => {
        btn.className = 'btn btn-secondary';
      });

      if (periodo === 'mes') {
        document.getElementById('filtroMesPerfil').className = 'btn btn-primary';
      } else if (periodo === 'ultimo') {
        document.getElementById('filtroUltimoPerfil').className = 'btn btn-primary';
      } else if (periodo === 'trimestre') {
        document.getElementById('filtroTrimestre').className = 'btn btn-primary';
      }

      console.log(`💰 Filtrando dados financeiros por: ${periodo}`);

      // Simular atualização dos valores
      const valores = {
        'mes': { pagar: 12450, receber: 28900, saldo: 16450 },
        'ultimo': { pagar: 8200, receber: 22100, saldo: 13900 },
        'trimestre': { pagar: 35600, receber: 78400, saldo: 42800 }
      };

      // Feedback visual
      const toast = document.createElement('div');
      toast.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #018820;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 1000;
      `;
      toast.textContent = `✅ Período atualizado: ${periodo === 'mes' ? 'Este mês' : periodo === 'ultimo' ? 'Último mês' : 'Últimos 3 meses'}`;
      document.body.appendChild(toast);
      setTimeout(() => toast.remove(), 3000);
    }

    // Relatório financeiro
    function abrirRelatorioFinanceiro() {
      const modal = document.createElement('div');
      modal.id = 'modalRelatorioFinanceiro';
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      `;

      modal.innerHTML = `
        <div style="background: white; border-radius: 12px; padding: 30px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
            <h2 style="margin: 0; color: #333;">📊 Relatório Financeiro</h2>
            <button onclick="fecharRelatorioFinanceiro()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
          </div>

          <div style="display: grid; gap: 20px;">

            <!-- Tipo de relatório -->
            <div>
              <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">📋 Tipo de Relatório</label>
              <select id="tipoRelatorioFinanceiro" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;">
                <option value="completo">📊 Relatório Completo</option>
                <option value="receitas">💰 Apenas Receitas</option>
                <option value="despesas">💸 Apenas Despesas</option>
                <option value="fluxo">📈 Fluxo de Caixa</option>
                <option value="impostos">🧾 Relatório de Impostos</option>
              </select>
            </div>

            <!-- Período -->
            <div>
              <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">📅 Período</label>
              <select id="periodoRelatorioFinanceiro" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;">
                <option value="mes_atual">Este mês</option>
                <option value="mes_anterior">Mês anterior</option>
                <option value="trimestre">Último trimestre</option>
                <option value="semestre">Último semestre</option>
                <option value="ano">Este ano</option>
                <option value="personalizado">Período personalizado</option>
              </select>
            </div>

            <!-- Formato -->
            <div>
              <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">📁 Formato</label>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; cursor: pointer;" onclick="selecionarFormatoFinanceiro('excel')">
                  <input type="radio" name="formatoFinanceiro" value="excel" checked style="margin: 0;">
                  <span>📊 Excel (.xlsx)</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; cursor: pointer;" onclick="selecionarFormatoFinanceiro('pdf')">
                  <input type="radio" name="formatoFinanceiro" value="pdf" style="margin: 0;">
                  <span>📄 PDF</span>
                </label>
              </div>
            </div>

          </div>

          <div style="display: flex; gap: 15px; margin-top: 30px;">
            <button onclick="fecharRelatorioFinanceiro()" style="flex: 1; padding: 12px; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer;">
              Cancelar
            </button>
            <button onclick="gerarRelatorioFinanceiro()" style="flex: 1; padding: 12px; background: #018820; color: white; border: none; border-radius: 8px; cursor: pointer;">
              📊 Gerar Relatório
            </button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
    }

    function fecharRelatorioFinanceiro() {
      const modal = document.getElementById('modalRelatorioFinanceiro');
      if (modal) modal.remove();
    }

    function selecionarFormatoFinanceiro(formato) {
      const radios = document.querySelectorAll('input[name="formatoFinanceiro"]');
      radios.forEach(radio => {
        radio.checked = radio.value === formato;
        radio.parentElement.style.borderColor = radio.checked ? '#018820' : '#e0e0e0';
        radio.parentElement.style.background = radio.checked ? '#f0fdf4' : 'white';
      });
    }

    async function gerarRelatorioFinanceiro() {
      const tipo = document.getElementById('tipoRelatorioFinanceiro').value;
      const periodo = document.getElementById('periodoRelatorioFinanceiro').value;
      const formato = document.querySelector('input[name="formatoFinanceiro"]:checked').value;

      try {
        console.log('📊 Gerando relatório financeiro:', { tipo, periodo, formato });

        // Simular geração
        await new Promise(resolve => setTimeout(resolve, 2000));

        alert(`✅ Relatório financeiro gerado!\n\n📋 Tipo: ${tipo}\n📅 Período: ${periodo}\n📄 Formato: ${formato.toUpperCase()}\n\nO download iniciará automaticamente.`);

        fecharRelatorioFinanceiro();
      } catch (error) {
        alert('❌ Erro ao gerar relatório. Tente novamente.');
      }
    }

    // Adicionar conta bancária
    function adicionarContaBancaria() {
      const modal = document.createElement('div');
      modal.id = 'modalContaBancaria';
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      `;

      modal.innerHTML = `
        <div style="background: white; border-radius: 12px; padding: 30px; width: 90%; max-width: 500px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
            <h2 style="margin: 0; color: #333;">🏦 Adicionar Conta Bancária</h2>
            <button onclick="fecharModalContaBancaria()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
          </div>

          <form id="formContaBancaria" onsubmit="salvarContaBancaria(event)">
            <div style="display: grid; gap: 20px;">

              <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">🏦 Banco</label>
                <select id="bancoInput" required style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;">
                  <option value="">Selecione o banco</option>
                  <option value="001">Banco do Brasil</option>
                  <option value="341">Itaú</option>
                  <option value="033">Santander</option>
                  <option value="104">Caixa Econômica</option>
                  <option value="237">Bradesco</option>
                  <option value="260">Nu Pagamentos</option>
                </select>
              </div>

              <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">🏢 Agência</label>
                <input type="text" id="agenciaInput" required style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;" placeholder="Ex: 1234-5">
              </div>

              <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">💳 Conta</label>
                <input type="text" id="contaInput" required style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;" placeholder="Ex: 67890-1">
              </div>

              <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">📋 Tipo de Conta</label>
                <select id="tipoContaInput" required style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;">
                  <option value="corrente">Conta Corrente</option>
                  <option value="poupanca">Conta Poupança</option>
                </select>
              </div>

            </div>

            <div style="display: flex; gap: 15px; margin-top: 30px;">
              <button type="button" onclick="fecharModalContaBancaria()" style="flex: 1; padding: 12px; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer;">
                Cancelar
              </button>
              <button type="submit" style="flex: 1; padding: 12px; background: #018820; color: white; border: none; border-radius: 8px; cursor: pointer;">
                💾 Salvar Conta
              </button>
            </div>
          </form>
        </div>
      `;

      document.body.appendChild(modal);
    }

    function fecharModalContaBancaria() {
      const modal = document.getElementById('modalContaBancaria');
      if (modal) modal.remove();
    }

    async function salvarContaBancaria(event) {
      event.preventDefault();

      const formData = {
        banco: document.getElementById('bancoInput').value,
        agencia: document.getElementById('agenciaInput').value,
        conta: document.getElementById('contaInput').value,
        tipo: document.getElementById('tipoContaInput').value
      };

      try {
        console.log('💾 Salvando conta bancária:', formData);
        await new Promise(resolve => setTimeout(resolve, 1000));

        alert('✅ Conta bancária adicionada com sucesso!');
        fecharModalContaBancaria();
        location.reload();
      } catch (error) {
        alert('❌ Erro ao adicionar conta. Tente novamente.');
      }
    }

    // Adicionar método de transferência
    function adicionarMetodoTransferencia() {
      alert('🏦 Adicionar Método de Transferência\n\nModal para adicionar:\n• PIX\n• TED/DOC\n• Outros métodos');
    }

    // Adicionar link de pagamento
    function adicionarLinkPagamento() {
      alert('🔗 Adicionar Link de Pagamento\n\nModal para configurar:\n• Mercado Pago\n• PagSeguro\n• PayPal\n• Outros gateways');
    }

    // Editar informações pessoais
    function editarInformacoesPessoais() {
      alert('✏️ Editar Informações Pessoais\n\nModal para editar:\n• Nome\n• Email\n• Telefone\n• Documento');
    }

    // Salvar alterações
    function salvarAlteracoes() {
      alert('💾 Salvando alterações...\n\nTodas as informações foram atualizadas com sucesso!');
    }

    // Alterar senha
    function alterarSenha() {
      const modal = document.createElement('div');
      modal.id = 'modalAlterarSenha';
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      `;

      modal.innerHTML = `
        <div style="background: white; border-radius: 12px; padding: 30px; width: 90%; max-width: 500px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
            <h2 style="margin: 0; color: #333;">🔒 Alterar Senha</h2>
            <button onclick="fecharModalAlterarSenha()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
          </div>

          <form id="formAlterarSenha" onsubmit="salvarNovaSenha(event)">
            <div style="display: grid; gap: 20px;">

              <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">🔐 Senha Atual</label>
                <input type="password" id="senhaAtualInput" required style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;" placeholder="Digite sua senha atual">
              </div>

              <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">🆕 Nova Senha</label>
                <input type="password" id="novaSenhaInput" required minlength="6" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;" placeholder="Digite a nova senha">
              </div>

              <div>
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">✅ Confirmar Nova Senha</label>
                <input type="password" id="confirmarSenhaInput" required minlength="6" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;" placeholder="Confirme a nova senha">
              </div>

              <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; border: 2px solid #3b82f6;">
                <div style="font-weight: 600; color: #333; margin-bottom: 5px;">🛡️ Requisitos de Segurança:</div>
                <div style="color: #666; font-size: 14px;">
                  • Mínimo 6 caracteres<br>
                  • Recomendado: letras, números e símbolos
                </div>
              </div>

            </div>

            <div style="display: flex; gap: 15px; margin-top: 30px;">
              <button type="button" onclick="fecharModalAlterarSenha()" style="flex: 1; padding: 12px; background: #6b7280; color: white; border: none; border-radius: 8px; cursor: pointer;">
                Cancelar
              </button>
              <button type="submit" style="flex: 1; padding: 12px; background: #018820; color: white; border: none; border-radius: 8px; cursor: pointer;">
                🔒 Alterar Senha
              </button>
            </div>
          </form>
        </div>
      `;

      document.body.appendChild(modal);
    }

    function fecharModalAlterarSenha() {
      const modal = document.getElementById('modalAlterarSenha');
      if (modal) modal.remove();
    }

    async function salvarNovaSenha(event) {
      event.preventDefault();

      const senhaAtual = document.getElementById('senhaAtualInput').value;
      const novaSenha = document.getElementById('novaSenhaInput').value;
      const confirmarSenha = document.getElementById('confirmarSenhaInput').value;

      if (novaSenha !== confirmarSenha) {
        alert('❌ As senhas não coincidem. Tente novamente.');
        return;
      }

      try {
        console.log('🔒 Alterando senha...');
        await new Promise(resolve => setTimeout(resolve, 1500));

        alert('✅ Senha alterada com sucesso!\n\nPor segurança, você será redirecionado para fazer login novamente.');
        fecharModalAlterarSenha();

        // Simular logout
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
      } catch (error) {
        alert('❌ Erro ao alterar senha. Tente novamente.');
      }
    }

    // Baixar dados
    function baixarDados() {
      alert('📄 Preparando download dos seus dados...\n\nEm breve você receberá um arquivo ZIP com:\n• Dados pessoais\n• Histórico de vendas\n• Relatórios financeiros\n• Configurações da conta');

      setTimeout(() => {
        alert('✅ Download iniciado!\n\nVerifique sua pasta de downloads.');
      }, 3000);
    }

    console.log('👤 Página de perfil carregada com funcionalidades completas');
  </script>
</body>
</html>
