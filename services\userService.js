// services/userService.js

const User = require('../models/userModel');

class UserService {
  static async getAllUsers() {
    try {
      return await User.getAll();
    } catch (error) {
      throw new Error(`Erro ao buscar usuários: ${error.message}`);
    }
  }

  static async getUserById(id) {
    try {
      if (!id || isNaN(id)) {
        throw new Error('ID inválido');
      }
      return await User.getById(id);
    } catch (error) {
      throw new Error(`Erro ao buscar usuário: ${error.message}`);
    }
  }

  static async createUser(name, email) {
    try {
      if (!name || !email) {
        throw new Error('Nome e email são obrigatórios');
      }
      
      if (!this.isValidEmail(email)) {
        throw new Error('Email inválido');
      }

      const userData = { name, email };
      return await User.create(userData);
    } catch (error) {
      throw new Error(`Erro ao criar usuário: ${error.message}`);
    }
  }

  static async updateUser(id, name, email) {
    try {
      if (!id || isNaN(id)) {
        throw new Error('ID inválido');
      }
      
      if (!name || !email) {
        throw new Error('Nome e email são obrigatórios');
      }
      
      if (!this.isValidEmail(email)) {
        throw new Error('Email inválido');
      }

      const userData = { name, email };
      return await User.update(id, userData);
    } catch (error) {
      throw new Error(`Erro ao atualizar usuário: ${error.message}`);
    }
  }

  static async deleteUser(id) {
    try {
      if (!id || isNaN(id)) {
        throw new Error('ID inválido');
      }
      return await User.delete(id);
    } catch (error) {
      throw new Error(`Erro ao deletar usuário: ${error.message}`);
    }
  }

  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

module.exports = UserService;
