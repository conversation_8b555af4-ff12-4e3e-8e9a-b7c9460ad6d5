<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f5f5;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .cadastro-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 500px;
      text-align: center;
    }

    .logo-title {
      font-size: 36px;
      font-weight: bold;
      color: #018820;
      margin-bottom: 20px;
      letter-spacing: 2px;
    }

    .subtitle {
      font-size: 18px;
      color: #666;
      margin-bottom: 30px;
    }

    .form-group {
      margin-bottom: 20px;
      text-align: left;
    }

    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 6px;
    }

    .form-input {
      width: 100%;
      padding: 12px;
      border: 2px solid #e0e0e0;
      border-radius: 6px;
      font-size: 16px;
      transition: border-color 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: #018820;
    }

    .form-select {
      width: 100%;
      padding: 12px;
      border: 2px solid #e0e0e0;
      border-radius: 6px;
      font-size: 16px;
      background: white;
      transition: border-color 0.3s ease;
    }

    .form-select:focus {
      outline: none;
      border-color: #018820;
    }

    .cadastro-button {
      width: 100%;
      padding: 15px;
      background: #018820;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-top: 10px;
    }

    .cadastro-button:hover {
      background: #016a1a;
    }

    .login-link {
      margin-top: 20px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      font-size: 14px;
      color: #666;
    }

    .login-link a {
      color: #018820;
      text-decoration: none;
      font-weight: bold;
    }

    .login-link a:hover {
      text-decoration: underline;
    }

    .error-message {
      background: #fee;
      border: 1px solid #fcc;
      color: #c33;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 20px;
      font-size: 14px;
    }

    .success-message {
      background: #dff0d8;
      border: 1px solid #d6e9c6;
      color: #3c763d;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 20px;
      font-size: 14px;
    }

    .form-row {
      display: flex;
      gap: 15px;
    }

    .form-row .form-group {
      flex: 1;
    }

    .required {
      color: #c33;
    }

    .loading {
      opacity: 0.7;
      pointer-events: none;
    }

    .loading .cadastro-button {
      background: #ccc;
    }

    @media (max-width: 600px) {
      .form-row {
        flex-direction: column;
        gap: 0;
      }

      .cadastro-container {
        padding: 30px 20px;
      }
    }
  </style>
</head>
<body>
  <div class="cadastro-container">
    <!-- Logo: Texto grande "PCR LABOR" no topo -->
    <h1 class="logo-title">PCR LABOR</h1>
    <p class="subtitle">Criar Nova Conta</p>

    <!-- Mensagens de erro ou sucesso -->
    <% if (typeof error !== 'undefined') { %>
      <div class="error-message">
        <%= error %>
      </div>
    <% } %>

    <% if (typeof success !== 'undefined') { %>
      <div class="success-message">
        <%= success %>
      </div>
    <% } %>

    <form id="cadastroForm" action="/cadastro" method="POST">
      <!-- Nome Completo -->
      <div class="form-group">
        <label for="nome" class="form-label">Nome Completo <span class="required">*</span></label>
        <input
          type="text"
          id="nome"
          name="nome"
          class="form-input"
          placeholder="Digite seu nome completo"
          required
          minlength="2"
        >
      </div>

      <!-- Email -->
      <div class="form-group">
        <label for="email" class="form-label">Email <span class="required">*</span></label>
        <input
          type="email"
          id="email"
          name="email"
          class="form-input"
          placeholder="Digite seu email"
          required
        >
      </div>

      <!-- Senha e Confirmar Senha -->
      <div class="form-row">
        <div class="form-group">
          <label for="senha" class="form-label">Senha <span class="required">*</span></label>
          <input
            type="password"
            id="senha"
            name="senha"
            class="form-input"
            placeholder="Digite sua senha"
            required
            minlength="6"
          >
        </div>
        <div class="form-group">
          <label for="confirmarSenha" class="form-label">Confirmar Senha <span class="required">*</span></label>
          <input
            type="password"
            id="confirmarSenha"
            name="confirmarSenha"
            class="form-input"
            placeholder="Confirme sua senha"
            required
          >
        </div>
      </div>

      <!-- Campos removidos: telefone e cargo não existem na tabela atual -->

      <!-- Empresa -->
      <div class="form-group">
        <label for="empresa" class="form-label">Empresa <span class="required">*</span></label>
        <select id="empresa" name="id_empresa" class="form-select" required>
          <option value="">Selecione uma empresa</option>
          <% if (typeof empresas !== 'undefined' && empresas.length > 0) { %>
            <% empresas.forEach(empresa => { %>
              <option value="<%= empresa.id_empresa %>"><%= empresa.nome_fantasia %></option>
            <% }); %>
          <% } else { %>
            <option value="1">PCR Labor</option>
          <% } %>
        </select>
      </div>

      <!-- Botão de Cadastro -->
      <button type="submit" class="cadastro-button">
        CRIAR CONTA
      </button>
    </form>

    <!-- Link para Login -->
    <div class="login-link">
      Já tem uma conta? <a href="/login">Faça login aqui</a>
    </div>
  </div>

  <script>
    // Validação de senha
    document.getElementById('cadastroForm').addEventListener('submit', function(e) {
      const senha = document.getElementById('senha').value;
      const confirmarSenha = document.getElementById('confirmarSenha').value;

      if (senha !== confirmarSenha) {
        e.preventDefault();
        alert('As senhas não coincidem!');
        return false;
      }

      if (senha.length < 6) {
        e.preventDefault();
        alert('A senha deve ter pelo menos 6 caracteres!');
        return false;
      }

      // Mostrar loading
      document.querySelector('.cadastro-container').classList.add('loading');
    });

    // Máscara para telefone removida - campo não existe

    // Validação em tempo real
    document.getElementById('confirmarSenha').addEventListener('input', function(e) {
      const senha = document.getElementById('senha').value;
      const confirmarSenha = e.target.value;

      if (confirmarSenha && senha !== confirmarSenha) {
        e.target.style.borderColor = '#dc3545';
      } else {
        e.target.style.borderColor = '#e0e0e0';
      }
    });
  </script>
</body>
</html>
