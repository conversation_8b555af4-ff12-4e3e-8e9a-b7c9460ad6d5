# Projeto Individual PCR-Labor

Este é um projeto individual desenvolvido para a empresa PCR-Labor, focado em análises laboratoriais utilizando tecnologia PCR. O projeto segue o padrão arquitetural MVC (Model-View-Controller) para uma melhor organização e manutenibilidade do código.

## Funcionalidades

- ✅ Arquitetura MVC bem estruturada
- ✅ API RESTful para gerenciamento de usuários
- ✅ Interface web responsiva
- ✅ Validação de dados
- ✅ Tratamento de erros
- ✅ Testes automatizados
- ✅ Configuração de ambiente
- ✅ Documentação completa

## Tecnologias Utilizadas

- **Backend**: Node.js + Express.js
- **Banco de Dados**: PostgreSQL
- **Template Engine**: EJS
- **Testes**: Jest + Supertest
- **Desenvolvimento**: Nodemon
- **Estilo**: CSS3

## Estrutura do Projeto MVC

```
├── assets/                 # Arquivos estáticos (imagens, logos)
├── config/                 # Configurações da aplicação
│   └── db.js              # Configuração do banco de dados
├── controllers/           # Controladores (lógica de negócio)
│   ├── homeController.js  # Controller para páginas principais
│   └── userController.js  # Controller para operações de usuário
├── models/                # Modelos de dados
│   └── userModel.js       # Modelo de usuário
├── services/              # Camada de serviços
│   └── userService.js     # Serviços de usuário
├── routes/                # Definição das rotas
│   ├── index.js           # Rotas principais
│   └── userRoutes.js      # Rotas da API de usuários
├── views/                 # Templates e visualizações
│   ├── components/        # Componentes reutilizáveis
│   │   └── header.ejs     # Cabeçalho da aplicação
│   ├── css/               # Arquivos de estilo
│   │   └── style.css      # Estilos principais
│   ├── layout/            # Layouts base
│   │   └── main.ejs       # Layout principal
│   └── pages/             # Páginas da aplicação
│       ├── home.ejs       # Página inicial
│       ├── about.ejs      # Página sobre
│       └── contact.ejs    # Página de contato
├── tests/                 # Testes automatizados
├── scripts/               # Scripts utilitários
└── server.js              # Arquivo principal do servidor
```

## Padrão MVC Implementado

### Model (Modelo)
- **Localização**: `models/` e `services/`
- **Responsabilidade**: Gerenciar dados e lógica de negócio
- **Exemplo**: `userModel.js` para operações de banco de dados, `userService.js` para validações e regras de negócio

### View (Visualização)
- **Localização**: `views/`
- **Responsabilidade**: Apresentar dados ao usuário
- **Tecnologia**: EJS (Embedded JavaScript Templates)
- **Estrutura**: Layout principal com componentes reutilizáveis

### Controller (Controlador)
- **Localização**: `controllers/`
- **Responsabilidade**: Intermediar Model e View, processar requisições
- **Exemplo**: `homeController.js` para páginas web, `userController.js` para API REST

## Configuração e Instalação

### 1. Clone o repositório
```bash
git clone https://github.com/c4uazinnnn/Projeto-Individual-Pcr-Labor.git
cd Projeto-Individual-Pcr-Labor
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure as variáveis de ambiente
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

### 4. Configure o banco de dados
```bash
npm run init-db
```

### 5. Execute a aplicação
```bash
# Modo desenvolvimento
npm run dev

# Modo produção
npm start
```

## API Endpoints

### Usuários
- `GET /api/users` - Listar todos os usuários
- `GET /api/users/:id` - Buscar usuário por ID
- `POST /api/users` - Criar novo usuário
- `PUT /api/users/:id` - Atualizar usuário
- `DELETE /api/users/:id` - Deletar usuário

### Páginas Web
- `GET /` - Página inicial
- `GET /sobre` - Página sobre a empresa
- `GET /contato` - Página de contato

## Testes

```bash
# Executar todos os testes
npm test

# Executar testes com coverage
npm run test:coverage
```

## Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request
