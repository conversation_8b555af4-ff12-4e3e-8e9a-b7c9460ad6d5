/* ===== CSS SEGUINDO WIREFRAME EXATO ===== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

/* ===== HEADER HORIZONTAL (TOPO) ===== */
.header-horizontal {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
}

/* Logo "PCR LABOR" */
.header-logo {
  font-size: 24px;
  font-weight: bold;
  color: #018820;
  letter-spacing: 1px;
}

/* Menu de navegação */
.header-nav {
  display: flex;
  align-items: center;
  gap: 30px;
  list-style: none;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  color: #333;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  background: #018820;
  color: white;
}

.nav-icon {
  font-size: 16px;
}

/* ===== LAYOUT PRINCIPAL ===== */
.main-layout {
  display: flex;
  margin-top: 70px;
  min-height: calc(100vh - 70px);
}

/* Área central do conteúdo */
.content-area {
  flex: 1;
  padding: 30px;
  margin-right: 300px; /* Espaço para sidebar direita */
}

/* ===== LATERAL DIREITA (SEMPRE PRESENTE) ===== */
.sidebar-right {
  width: 300px;
  background: white;
  border-left: 1px solid #e0e0e0;
  position: fixed;
  top: 70px;
  right: 0;
  height: calc(100vh - 70px);
  overflow-y: auto;
  padding: 20px;
}

/* Tarefas */
.tasks-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
  font-size: 14px;
}

.task-checkbox {
  margin: 0;
}

/* Calendário */
.calendar-section {
  margin-bottom: 30px;
}

.mini-calendar {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.calendar-header {
  text-align: center;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  font-size: 12px;
}

.calendar-day {
  text-align: center;
  padding: 6px 4px;
  border-radius: 4px;
  cursor: pointer;
}

.calendar-day.today {
  background: #018820;
  color: white;
}

.calendar-day:hover {
  background: #e0e0e0;
}

/* Botão IA */
.ai-section {
  margin-bottom: 30px;
}

.ai-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #018820, #02a025);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.ai-button:hover {
  transform: translateY(-2px);
}

/* ===== CONTEÚDO DAS PÁGINAS ===== */
.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 30px;
}

/* Cards de resumo */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 25px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: #018820;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 24px;
  color: white;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #018820;
}

/* Seções de conteúdo */
.content-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  overflow: hidden;
}

.section-header {
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-content {
  padding: 25px;
}

/* Botões */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: #018820;
  color: white;
}

.btn-primary:hover {
  background: #016a1a;
}

.btn-secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
  background: #e9ecef;
}

/* Gráficos */
.chart-container {
  position: relative;
  height: 300px;
  margin: 20px 0;
}

/* Tabelas */
.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #666;
  font-size: 12px;
  text-transform: uppercase;
  border-bottom: 1px solid #e0e0e0;
}

.data-table td {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
}

.data-table tr:hover {
  background: #f8f9fa;
}

/* ===== RESPONSIVO ===== */
@media (max-width: 1024px) {
  .sidebar-right {
    display: none;
  }
  
  .content-area {
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .header-nav {
    display: none;
  }
  
  .content-area {
    padding: 20px 15px;
  }
  
  .cards-grid {
    grid-template-columns: 1fr;
  }
}
