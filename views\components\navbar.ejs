<!-- NAVBAR NO TOPO SEGUINDO WIREFRAME -->
<nav class="top-navbar">
  <!-- Logo e Título -->
  <div class="navbar-logo">
    <img src="/assets/LogoPCR.png" alt="PCR Labor">
    <span class="navbar-title">PCR Labor</span>
  </div>

  <!-- Navegação Principal -->
  <ul class="navbar-nav">
    <li class="nav-item">
      <a href="/dashboard" class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>">
        <span class="nav-icon">📊</span>
        <span>Dashboard</span>
      </a>
    </li>

    <li class="nav-item">
      <a href="/vendas" class="nav-link <%= currentPage === 'vendas' ? 'active' : '' %>">
        <span class="nav-icon">💰</span>
        <span>Vendas</span>
      </a>
    </li>

    <li class="nav-item">
      <a href="/estoque" class="nav-link <%= currentPage === 'estoque' ? 'active' : '' %>">
        <span class="nav-icon">📦</span>
        <span>Estoque</span>
      </a>
    </li>

    <li class="nav-item">
      <a href="/produtos" class="nav-link <%= currentPage === 'produtos' ? 'active' : '' %>">
        <span class="nav-icon">🧪</span>
        <span>Produtos</span>
      </a>
    </li>

    <li class="nav-item">
      <a href="/pedidos" class="nav-link <%= currentPage === 'pedidos' ? 'active' : '' %>">
        <span class="nav-icon">📋</span>
        <span>Pedidos</span>
      </a>
    </li>

    <li class="nav-item">
      <a href="/plataformas" class="nav-link <%= currentPage === 'plataformas' ? 'active' : '' %>">
        <span class="nav-icon">🌐</span>
        <span>Plataformas</span>
      </a>
    </li>

    <li class="nav-item">
      <a href="/perfil" class="nav-link <%= currentPage === 'perfil' ? 'active' : '' %>">
        <span class="nav-icon">👤</span>
        <span>Perfil</span>
      </a>
    </li>
  </ul>

  <!-- Área do Usuário -->
  <div class="navbar-user">
    <!-- Busca -->
    <div class="search-box">
      <input type="text" class="search-input" placeholder="Buscar...">
      <span>🔍</span>
    </div>

    <!-- Info do Usuário -->
    <div class="user-info" onclick="toggleUserMenu()">
      <div class="user-avatar">A</div>
      <span>Admin</span>
      <span>▼</span>
    </div>

    <!-- Menu do Usuário (dropdown) -->
    <div id="userMenu" style="display: none; position: absolute; top: 60px; right: 30px; background: white; border: 1px solid #e0e0e0; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); padding: 10px; min-width: 150px; z-index: 1001;">
      <a href="/perfil" style="display: block; padding: 8px 12px; color: #333; text-decoration: none; border-radius: 4px;">
        👤 Perfil
      </a>
      <a href="/logout" style="display: block; padding: 8px 12px; color: #333; text-decoration: none; border-radius: 4px;">
        🚪 Sair
      </a>
    </div>
  </div>
</nav>

<script>
  function toggleUserMenu() {
    const menu = document.getElementById('userMenu');
    menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
  }

  // Fechar menu ao clicar fora
  document.addEventListener('click', function(event) {
    const userInfo = document.querySelector('.user-info');
    const userMenu = document.getElementById('userMenu');

    if (!userInfo.contains(event.target)) {
      userMenu.style.display = 'none';
    }
  });
</script>
