<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Cadastro - PCR Labor</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .test-section h3 { margin-top: 0; color: #333; }
        button { padding: 12px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 5px; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        input { width: 100%; padding: 10px; margin: 8px 0; border: 1px solid #ddd; border-radius: 5px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste de Cadastro - PCR Labor</h1>
        
        <!-- Teste de Cadastro via Formulário -->
        <div class="test-section">
            <h3>📝 Teste de Cadastro via Formulário</h3>
            <p>Teste o cadastro preenchendo os dados abaixo:</p>
            
            <div class="form-group">
                <label for="nome">Nome Completo:</label>
                <input type="text" id="nome" value="João Teste Silva">
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="senha">Senha:</label>
                <input type="password" id="senha" value="123456">
            </div>
            
            <div class="form-group">
                <label for="confirmarSenha">Confirmar Senha:</label>
                <input type="password" id="confirmarSenha" value="123456">
            </div>
            
            <div class="form-group">
                <label for="empresa">ID da Empresa:</label>
                <input type="number" id="empresa" value="1">
            </div>
            
            <button onclick="testarCadastro()">🚀 Testar Cadastro</button>
            <button onclick="gerarDadosAleatorios()">🎲 Gerar Dados Aleatórios</button>
            
            <div id="result-cadastro" class="result" style="display: none;"></div>
        </div>

        <!-- Links Úteis -->
        <div class="test-section">
            <h3>🔗 Links Úteis</h3>
            <button onclick="abrirPagina('/cadastro')">📝 Abrir Página de Cadastro</button>
            <button onclick="abrirPagina('/login')">🔐 Abrir Página de Login</button>
            <button onclick="abrirPagina('/api/usuarios')">👥 Ver Usuários (API)</button>
            <button onclick="abrirPagina('/api/empresas')">🏢 Ver Empresas (API)</button>
        </div>

        <!-- Teste de Login -->
        <div class="test-section">
            <h3>🔐 Teste de Login</h3>
            <p>Após criar o usuário, teste o login:</p>
            
            <div class="form-group">
                <label for="loginEmail">Email:</label>
                <input type="email" id="loginEmail" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="loginSenha">Senha:</label>
                <input type="password" id="loginSenha" value="123456">
            </div>
            
            <button onclick="testarLogin()">🔑 Testar Login</button>
            
            <div id="result-login" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function testarCadastro() {
            const resultDiv = document.getElementById('result-cadastro');
            
            try {
                const dados = {
                    nome: document.getElementById('nome').value,
                    email: document.getElementById('email').value,
                    senha: document.getElementById('senha').value,
                    confirmarSenha: document.getElementById('confirmarSenha').value,
                    id_empresa: parseInt(document.getElementById('empresa').value)
                };

                // Validações básicas
                if (!dados.nome || !dados.email || !dados.senha) {
                    throw new Error('Todos os campos obrigatórios devem ser preenchidos');
                }

                if (dados.senha !== dados.confirmarSenha) {
                    throw new Error('As senhas não coincidem');
                }

                if (dados.senha.length < 6) {
                    throw new Error('A senha deve ter pelo menos 6 caracteres');
                }

                // Enviar dados para o servidor
                const response = await fetch('/cadastro', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(dados)
                });

                const responseText = await response.text();
                
                resultDiv.style.display = 'block';
                if (response.ok) {
                    resultDiv.className = 'result success';
                    if (responseText.includes('sucesso') || responseText.includes('login')) {
                        resultDiv.textContent = '✅ Cadastro realizado com sucesso!\nUsuário criado e redirecionado para login.';
                        // Atualizar campos de login
                        document.getElementById('loginEmail').value = dados.email;
                        document.getElementById('loginSenha').value = dados.senha;
                    } else {
                        resultDiv.textContent = '✅ Resposta do servidor:\n' + responseText.substring(0, 500);
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Erro no cadastro:\n' + responseText.substring(0, 500);
                }
                
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Erro: ' + error.message;
            }
        }

        async function testarLogin() {
            const resultDiv = document.getElementById('result-login');
            
            try {
                const dados = {
                    email: document.getElementById('loginEmail').value,
                    senha: document.getElementById('loginSenha').value
                };

                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(dados)
                });

                const responseText = await response.text();
                
                resultDiv.style.display = 'block';
                if (response.ok) {
                    resultDiv.className = 'result success';
                    if (responseText.includes('dashboard') || response.url.includes('dashboard')) {
                        resultDiv.textContent = '✅ Login realizado com sucesso!\nRedirecionado para o dashboard.';
                    } else {
                        resultDiv.textContent = '✅ Resposta do servidor:\n' + responseText.substring(0, 500);
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Erro no login:\n' + responseText.substring(0, 500);
                }
                
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Erro: ' + error.message;
            }
        }

        function gerarDadosAleatorios() {
            const timestamp = Date.now().toString().slice(-4);
            const nomes = ['João', 'Maria', 'Pedro', 'Ana', 'Carlos', 'Lucia'];
            const sobrenomes = ['Silva', 'Santos', 'Oliveira', 'Souza', 'Lima', 'Costa'];
            
            const nome = nomes[Math.floor(Math.random() * nomes.length)];
            const sobrenome = sobrenomes[Math.floor(Math.random() * sobrenomes.length)];
            const nomeCompleto = `${nome} ${sobrenome} ${timestamp}`;
            const email = `${nome.toLowerCase()}.${sobrenome.toLowerCase()}${timestamp}@exemplo.com`;
            
            document.getElementById('nome').value = nomeCompleto;
            document.getElementById('email').value = email;
            document.getElementById('senha').value = '123456';
            document.getElementById('confirmarSenha').value = '123456';
            
            // Atualizar campos de login também
            document.getElementById('loginEmail').value = email;
            document.getElementById('loginSenha').value = '123456';
        }

        function abrirPagina(url) {
            window.open(url, '_blank');
        }

        // Gerar dados aleatórios ao carregar a página
        window.onload = function() {
            gerarDadosAleatorios();
        };
    </script>
</body>
</html>
