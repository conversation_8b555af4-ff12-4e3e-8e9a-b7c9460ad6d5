<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
  <style>
    .login-page {
      min-height: 100vh;
      background: linear-gradient(135deg, #018820 0%, #02a025 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .login-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      width: 100%;
      max-width: 400px;
    }

    .login-header {
      text-align: center;
      padding: 40px 30px 20px;
    }

    .login-logo {
      width: 80px;
      height: 80px;
      margin: 0 auto 15px;
      border-radius: 50%;
      object-fit: contain;
      background: #f8f9fa;
      padding: 10px;
    }

    .login-title {
      font-size: 24px;
      font-weight: bold;
      color: #018820;
      margin: 0 0 5px;
    }

    .login-subtitle {
      font-size: 14px;
      color: #666;
      margin: 0;
    }

    .login-form {
      padding: 20px 30px 30px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 6px;
    }

    .form-input {
      width: 100%;
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: #018820;
    }

    .login-button {
      width: 100%;
      padding: 12px;
      background: #018820;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-bottom: 15px;
    }

    .login-button:hover {
      background: #016a1a;
    }

    .demo-info {
      background: #f8f9fa;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 12px;
      font-size: 12px;
      color: #666;
      text-align: center;
    }

    .error-message {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      font-size: 14px;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <img src="/assets/LogoPCR.png" alt="PCR Labor" class="login-logo">
        <h1 class="login-title">PCR Labor</h1>
        <p class="login-subtitle">Sistema de Gestão</p>
      </div>

      <div class="login-form">
        <% if (typeof error !== 'undefined') { %>
          <div class="error-message">
            <%= error %>
          </div>
        <% } %>

        <form action="/login" method="POST">
          <div class="form-group">
            <label for="email" class="form-label">Email</label>
            <input 
              type="email" 
              id="email" 
              name="email" 
              class="form-input" 
              placeholder="Digite seu email"
              required
            >
          </div>

          <div class="form-group">
            <label for="senha" class="form-label">Senha</label>
            <input 
              type="password" 
              id="senha" 
              name="senha" 
              class="form-input" 
              placeholder="Digite sua senha"
              required
            >
          </div>

          <button type="submit" class="login-button">
            Entrar
          </button>
        </form>

        <div class="demo-info">
          <strong>Demo:</strong> <EMAIL> / admin123
        </div>
      </div>
    </div>
  </div>
</body>
</html>
