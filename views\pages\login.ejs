<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/style.css">
  <style>
    .login-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #018820, #02a025);
    }

    .login-box {
      background: white;
      padding: 40px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      width: 100%;
      max-width: 400px;
    }

    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .login-header img {
      width: 160px;
      height: 160px;
      margin-bottom: 20px;
      object-fit: contain;
      border-radius: 8px;
    }

    .login-header h1 {
      color: #018820;
      margin: 0;
      font-size: 24px;
    }

    .login-header p {
      color: #666;
      margin: 5px 0 0 0;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      color: #333;
      font-weight: bold;
    }

    .form-group input {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 5px;
      font-size: 16px;
      transition: border-color 0.3s;
    }

    .form-group input:focus {
      outline: none;
      border-color: #018820;
    }

    .login-btn {
      width: 100%;
      padding: 12px;
      background-color: #018820;
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .login-btn:hover {
      background-color: #016a1a;
    }

    .error-message {
      background-color: #f8d7da;
      color: #721c24;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 20px;
      text-align: center;
    }

    .demo-info {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 5px;
      font-size: 14px;
      color: #666;
    }

    .demo-info strong {
      color: #018820;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="/assets/LogoPCR.png" alt="Logo PCR Labor">
        <h1>PCR Labor</h1>
        <p>Sistema de Gestão</p>
      </div>

      <% if (typeof error !== 'undefined') { %>
        <div class="error-message">
          <%= error %>
        </div>
      <% } %>

      <form action="/login" method="POST">
        <div class="form-group">
          <label for="email">Email:</label>
          <input type="email" id="email" name="email" required>
        </div>

        <div class="form-group">
          <label for="senha">Senha:</label>
          <input type="password" id="senha" name="senha" required>
        </div>

        <button type="submit" class="login-btn">Entrar</button>
      </form>

      <div class="demo-info">
        <strong>Dados para demonstração:</strong><br>
        Email: <EMAIL><br>
        Senha: admin123
      </div>
    </div>
  </div>
</body>
</html>
