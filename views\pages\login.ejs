<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe.css">
  <style>
    .login-page {
      min-height: 100vh;
      background: linear-gradient(135deg, #018820 0%, #02a025 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .login-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 400px;
      overflow: hidden;
      animation: slideUp 0.6s ease-out;
    }

    .login-header {
      text-align: center;
      padding: 40px 40px 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .login-logo {
      width: 100px;
      height: 100px;
      margin: 0 auto 20px;
      border-radius: 50%;
      object-fit: contain;
      background: white;
      padding: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .login-title {
      font-size: 28px;
      font-weight: bold;
      color: #018820;
      margin: 0 0 8px;
    }

    .login-subtitle {
      font-size: 16px;
      color: #666;
      margin: 0;
    }

    .login-form {
      padding: 30px 40px 40px;
    }

    .form-group {
      margin-bottom: 24px;
    }

    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: #f8f9fa;
    }

    .form-input:focus {
      outline: none;
      border-color: #018820;
      background: white;
      box-shadow: 0 0 0 3px rgba(1, 136, 32, 0.1);
    }

    .login-button {
      width: 100%;
      padding: 14px;
      background: linear-gradient(135deg, #018820 0%, #02a025 100%);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 20px;
    }

    .login-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(1, 136, 32, 0.3);
    }

    .demo-credentials {
      background: #f8f9fa;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      font-size: 14px;
      color: #666;
    }

    .demo-credentials strong {
      color: #018820;
      display: block;
      margin-bottom: 8px;
    }

    .error-message {
      background: #fee;
      border: 1px solid #fcc;
      color: #c33;
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 20px;
      font-size: 14px;
      text-align: center;
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>
<body>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <img src="/assets/LogoPCR.png" alt="PCR Labor" class="login-logo">
        <h1 class="login-title">PCR Labor</h1>
        <p class="login-subtitle">Sistema de Gestão Laboratorial</p>
      </div>

      <div class="login-form">
        <% if (typeof error !== 'undefined') { %>
          <div class="error-message">
            <%= error %>
          </div>
        <% } %>

        <form action="/login" method="POST">
          <div class="form-group">
            <label for="email" class="form-label">Email</label>
            <input 
              type="email" 
              id="email" 
              name="email" 
              class="form-input" 
              placeholder="Digite seu email"
              required
            >
          </div>

          <div class="form-group">
            <label for="senha" class="form-label">Senha</label>
            <input 
              type="password" 
              id="senha" 
              name="senha" 
              class="form-input" 
              placeholder="Digite sua senha"
              required
            >
          </div>

          <button type="submit" class="login-button">
            Entrar no Sistema
          </button>
        </form>

        <div class="demo-credentials">
          <strong>🔑 Credenciais de Demonstração:</strong>
          Email: <EMAIL><br>
          Senha: admin123
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('email').focus();
    });

    document.querySelector('form').addEventListener('submit', function() {
      const button = document.querySelector('.login-button');
      button.innerHTML = '🔄 Entrando...';
      button.disabled = true;
    });
  </script>
</body>
</html>
