<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f5f5;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .login-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      padding: 60px 40px;
      width: 100%;
      max-width: 400px;
      text-align: center;
    }

    .logo-title {
      font-size: 36px;
      font-weight: bold;
      color: #018820;
      margin-bottom: 40px;
      letter-spacing: 2px;
    }

    .form-group {
      margin-bottom: 25px;
      text-align: left;
    }

    .form-label {
      display: block;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .form-input {
      width: 100%;
      padding: 15px;
      border: 2px solid #e0e0e0;
      border-radius: 6px;
      font-size: 16px;
      transition: border-color 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: #018820;
    }

    .login-button {
      width: 100%;
      padding: 15px;
      background: #018820;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-top: 10px;
    }

    .login-button:hover {
      background: #016a1a;
    }

    .error-message {
      background: #fee;
      border: 1px solid #fcc;
      color: #c33;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 20px;
      font-size: 14px;
    }

    .demo-info {
      margin-top: 30px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 6px;
      font-size: 14px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <!-- Logo: Texto grande "PCR LABOR" no topo -->
    <h1 class="logo-title">PCR LABOR</h1>

    <!-- Formulário de Login -->
    <% if (typeof error !== 'undefined') { %>
      <div class="error-message">
        <%= error %>
      </div>
    <% } %>

    <form action="/login" method="POST">
      <!-- Campo de Email: Rótulo "Email" e campo de input -->
      <div class="form-group">
        <label for="email" class="form-label">Email</label>
        <input
          type="email"
          id="email"
          name="email"
          class="form-input"
          placeholder="Digite seu email"
          required
        >
      </div>

      <!-- Campo de Senha: Rótulo "Senha" e campo de input -->
      <div class="form-group">
        <label for="senha" class="form-label">Senha</label>
        <input
          type="password"
          id="senha"
          name="senha"
          class="form-input"
          placeholder="Digite sua senha"
          required
        >
      </div>

      <!-- Botão "ENTRAR": Botão para enviar o formulário -->
      <button type="submit" class="login-button">
        ENTRAR
      </button>
    </form>

    <div class="demo-info">
      <strong>Demo:</strong> <EMAIL> / admin123
    </div>
  </div>
</body>
</html>
