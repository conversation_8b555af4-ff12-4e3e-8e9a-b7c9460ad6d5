<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe.css">
</head>
<body>
  <div class="app-container">
    <%- include('../components/sidebar') %>
    
    <div class="main-content">
      <%- include('../components/topbar') %>
      
      <div class="content-area">
        <div class="page-header">
          <h1 class="page-title">Vendas</h1>
          <p class="page-subtitle">Histórico e análise de vendas</p>
        </div>
        
        <!-- Cards de Resumo -->
        <div class="cards-grid">
          <div class="card">
            <div class="card-header">
              <div class="card-icon">💰</div>
              <div><h3 class="card-title">Total de Vendas</h3></div>
            </div>
            <div class="card-value"><%= vendas.length %></div>
            <div class="card-trend">Vendas realizadas</div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #d4edda;">📈</div>
              <div><h3 class="card-title">Faturamento</h3></div>
            </div>
            <div class="card-value" style="color: #155724;">
              R$ <%= vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0).toFixed(2) %>
            </div>
            <div class="card-trend">Receita total</div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #d1ecf1;">🎯</div>
              <div><h3 class="card-title">Ticket Médio</h3></div>
            </div>
            <div class="card-value" style="color: #0c5460;">
              R$ <%= vendas.length > 0 ? (vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0) / vendas.length).toFixed(2) : '0.00' %>
            </div>
            <div class="card-trend">Por venda</div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #fff3cd;">📅</div>
              <div><h3 class="card-title">Vendas Hoje</h3></div>
            </div>
            <div class="card-value" style="color: #856404;">
              <%= vendas.filter(v => {
                const hoje = new Date().toISOString().split('T')[0];
                const dataVenda = new Date(v.data).toISOString().split('T')[0];
                return dataVenda === hoje;
              }).length %>
            </div>
            <div class="card-trend">Vendas do dia</div>
          </div>
        </div>
        
        <!-- Filtros -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <div style="display: flex; gap: 15px;">
            <input type="date" id="dateFrom" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
            <input type="date" id="dateTo" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
            <select id="platformFilter" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
              <option value="">Todas as plataformas</option>
              <option value="Mercado Livre">Mercado Livre</option>
              <option value="Shopee">Shopee</option>
              <option value="Site Próprio">Site Próprio</option>
            </select>
          </div>
          <button class="btn btn-primary" onclick="openNewSaleModal()">
            ➕ Nova Venda
          </button>
        </div>
        
        <!-- Tabela de Vendas -->
        <div class="table-container">
          <div class="table-header">
            <h3 class="table-title">💰 Histórico de Vendas</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Quantidade</th>
                <th>Valor Total</th>
                <th>Plataforma</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody id="salesTableBody">
              <% if (vendas && vendas.length > 0) { %>
                <% vendas.forEach(venda => { %>
                  <tr>
                    <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    <td><strong><%= venda.produto_nome || 'N/A' %></strong></td>
                    <td><%= venda.quantidade %> unidades</td>
                    <td><strong>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></strong></td>
                    <td>
                      <span style="background: #f0f0f0; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        <%= venda.plataforma_nome || 'N/A' %>
                      </span>
                    </td>
                    <td>
                      <span style="background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        ✅ Concluída
                      </span>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="6" style="text-align: center; color: #666; padding: 40px;">
                    <div>
                      <div style="font-size: 48px; margin-bottom: 16px;">💰</div>
                      <div style="font-size: 18px; margin-bottom: 8px;">Nenhuma venda encontrada</div>
                      <div style="font-size: 14px; color: #999;">As vendas aparecerão aqui conforme forem realizadas</div>
                    </div>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Filtros de data
    document.getElementById('dateFrom').addEventListener('change', filterSales);
    document.getElementById('dateTo').addEventListener('change', filterSales);
    document.getElementById('platformFilter').addEventListener('change', filterSales);
    
    function filterSales() {
      const dateFrom = document.getElementById('dateFrom').value;
      const dateTo = document.getElementById('dateTo').value;
      const platform = document.getElementById('platformFilter').value;
      const rows = document.querySelectorAll('#salesTableBody tr');
      
      rows.forEach(row => {
        let show = true;
        const cells = row.querySelectorAll('td');
        
        if (cells.length > 1) {
          const dateCell = cells[0].textContent;
          const platformCell = cells[4].textContent.trim();
          
          // Filtro de data
          if (dateFrom || dateTo) {
            const saleDate = new Date(dateCell.split('/').reverse().join('-'));
            if (dateFrom && saleDate < new Date(dateFrom)) show = false;
            if (dateTo && saleDate > new Date(dateTo)) show = false;
          }
          
          // Filtro de plataforma
          if (platform && !platformCell.includes(platform)) {
            show = false;
          }
        }
        
        row.style.display = show ? '' : 'none';
      });
    }
    
    function openNewSaleModal() {
      alert('Modal de nova venda em desenvolvimento');
      // Implementar modal de nova venda
    }
  </script>
</body>
</html>
