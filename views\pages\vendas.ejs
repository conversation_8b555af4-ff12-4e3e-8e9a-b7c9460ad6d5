<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
</head>
<body>
  <div class="app-layout">
    <%- include('../components/sidebar') %>
    
    <div class="main-content">
      <%- include('../components/topbar') %>
      
      <div class="content-area">
        <!-- Cards de Métricas -->
        <div class="cards-grid">
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">💰</div>
              <h3 class="card-title">Total de Vendas</h3>
            </div>
            <div class="card-value"><%= vendas.length %></div>
            <div class="card-subtitle">vendas realizadas</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📈</div>
              <h3 class="card-title">Faturamento</h3>
            </div>
            <div class="card-value">R$ <%= vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0).toFixed(2) %></div>
            <div class="card-subtitle">receita total</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">🎯</div>
              <h3 class="card-title">Ticket Médio</h3>
            </div>
            <div class="card-value">R$ <%= vendas.length > 0 ? (vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0) / vendas.length).toFixed(2) : '0.00' %></div>
            <div class="card-subtitle">por venda</div>
          </div>
          
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📅</div>
              <h3 class="card-title">Vendas Hoje</h3>
            </div>
            <div class="card-value">
              <%= vendas.filter(v => {
                const hoje = new Date().toISOString().split('T')[0];
                const dataVenda = new Date(v.data).toISOString().split('T')[0];
                return dataVenda === hoje;
              }).length %>
            </div>
            <div class="card-subtitle">vendas do dia</div>
          </div>
        </div>
        
        <!-- Tabela de Vendas -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Histórico de Vendas</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Quantidade</th>
                <th>Valor Total</th>
                <th>Plataforma</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% if (vendas && vendas.length > 0) { %>
                <% vendas.forEach(venda => { %>
                  <tr>
                    <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    <td><%= venda.produto_nome || 'N/A' %></td>
                    <td><%= venda.quantidade %></td>
                    <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                    <td><%= venda.plataforma_nome || 'N/A' %></td>
                    <td>
                      <span style="color: #28a745;">Concluída</span>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="6" style="text-align: center; color: #666; padding: 40px;">
                    Nenhuma venda encontrada
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
