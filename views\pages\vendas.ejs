<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
  <style>
    .chart-container {
      position: relative;
      height: 300px;
      margin: 20px 0;
    }

    .sales-charts {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 20px;
      margin-bottom: 30px;
    }

    .chart-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .chart-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
    }

    .filter-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }

    .filter-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      align-items: end;
    }

    .form-group {
      margin-bottom: 0;
    }

    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 6px;
    }

    .form-input, .form-select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 14px;
    }

    .sync-button {
      background: linear-gradient(135deg, #018820, #02a025);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .sync-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(1, 136, 32, 0.3);
    }

    .sync-button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .status-indicator {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 12px;
    }

    .status-success {
      background: #dcfce7;
      color: #16a34a;
    }

    .status-warning {
      background: #fef3c7;
      color: #d97706;
    }

    .status-error {
      background: #fee2e2;
      color: #dc2626;
    }
  </style>
</head>
<body>
  <div class="app-layout">
    <%- include('../components/sidebar') %>

    <div class="main-content">
      <%- include('../components/topbar') %>

      <div class="content-area">
        <!-- Cards de Métricas -->
        <div class="cards-grid">
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">💰</div>
              <h3 class="card-title">Total de Vendas</h3>
            </div>
            <div class="card-value"><%= vendas.length %></div>
            <div class="card-subtitle">vendas realizadas</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📈</div>
              <h3 class="card-title">Faturamento</h3>
            </div>
            <div class="card-value">R$ <%= vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0).toFixed(2) %></div>
            <div class="card-subtitle">receita total</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">🎯</div>
              <h3 class="card-title">Ticket Médio</h3>
            </div>
            <div class="card-value">R$ <%= vendas.length > 0 ? (vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0) / vendas.length).toFixed(2) : '0.00' %></div>
            <div class="card-subtitle">por venda</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📅</div>
              <h3 class="card-title">Vendas Hoje</h3>
            </div>
            <div class="card-value">
              <%= vendas.filter(v => {
                const hoje = new Date().toISOString().split('T')[0];
                const dataVenda = new Date(v.data).toISOString().split('T')[0];
                return dataVenda === hoje;
              }).length %>
            </div>
            <div class="card-subtitle">vendas do dia</div>
          </div>
        </div>

        <!-- Filtros e Sincronização -->
        <div class="filter-section">
          <div class="filter-grid">
            <div class="form-group">
              <label for="dataInicio" class="form-label">Data Início</label>
              <input type="date" id="dataInicio" class="form-input" value="2024-01-01">
            </div>

            <div class="form-group">
              <label for="dataFim" class="form-label">Data Fim</label>
              <input type="date" id="dataFim" class="form-input" value="2024-12-31">
            </div>

            <div class="form-group">
              <label for="plataformaFiltro" class="form-label">Plataforma</label>
              <select id="plataformaFiltro" class="form-select">
                <option value="">Todas as Plataformas</option>
                <option value="Mercado Livre">Mercado Livre</option>
                <option value="Shopee">Shopee</option>
                <option value="PCR Labor">PCR Labor</option>
              </select>
            </div>

            <div class="form-group">
              <button class="btn btn-primary" onclick="filtrarVendas()">
                🔍 Filtrar
              </button>
            </div>

            <div class="form-group">
              <button class="sync-button" id="syncButton" onclick="sincronizarPlataformas()">
                🔄 Sincronizar Plataformas
              </button>
            </div>

            <div class="form-group">
              <div id="syncStatus" class="status-indicator status-success" style="display: none;">
                ✅ Sincronizado
              </div>
            </div>
          </div>
        </div>

        <!-- Gráficos de Vendas -->
        <div class="sales-charts">
          <!-- Gráfico de Vendas por Período -->
          <div class="chart-section">
            <h3 class="chart-title">Vendas por Período</h3>
            <div class="chart-container">
              <canvas id="vendasPeriodoChart"></canvas>
            </div>
          </div>

          <!-- Gráfico de Vendas por Plataforma -->
          <div class="chart-section">
            <h3 class="chart-title">Vendas por Plataforma</h3>
            <div class="chart-container">
              <canvas id="vendasPlataformaChart"></canvas>
            </div>
          </div>
        </div>

        <!-- Tabela de Vendas -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Histórico de Vendas</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Quantidade</th>
                <th>Valor Total</th>
                <th>Plataforma</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% if (vendas && vendas.length > 0) { %>
                <% vendas.forEach(venda => { %>
                  <tr>
                    <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    <td><%= venda.produto_nome || 'N/A' %></td>
                    <td><%= venda.quantidade %></td>
                    <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                    <td><%= venda.plataforma_nome || 'N/A' %></td>
                    <td>
                      <span style="color: #28a745;">Concluída</span>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="6" style="text-align: center; color: #666; padding: 40px;">
                    Nenhuma venda encontrada
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Dados para os gráficos
    const vendasData = <%- JSON.stringify(vendas) %>;

    // Configurar datas padrão
    document.getElementById('dataFim').value = new Date().toISOString().split('T')[0];
    const dataInicio = new Date();
    dataInicio.setMonth(dataInicio.getMonth() - 6);
    document.getElementById('dataInicio').value = dataInicio.toISOString().split('T')[0];

    // Gráfico de Vendas por Período
    const periodoCtx = document.getElementById('vendasPeriodoChart').getContext('2d');

    // Processar dados por mês
    const vendasPorMes = processarVendasPorMes(vendasData);

    new Chart(periodoCtx, {
      type: 'line',
      data: {
        labels: vendasPorMes.labels,
        datasets: [{
          label: 'Vendas',
          data: vendasPorMes.vendas,
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4
        }, {
          label: 'Faturamento (R$)',
          data: vendasPorMes.faturamento,
          borderColor: '#f59e0b',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderWidth: 3,
          fill: false,
          tension: 0.4,
          yAxisID: 'y1'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          mode: 'index',
          intersect: false,
        },
        plugins: {
          legend: {
            position: 'top',
          }
        },
        scales: {
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            beginAtZero: true,
            title: {
              display: true,
              text: 'Quantidade de Vendas'
            }
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            beginAtZero: true,
            title: {
              display: true,
              text: 'Faturamento (R$)'
            },
            grid: {
              drawOnChartArea: false,
            },
          }
        }
      }
    });

    // Gráfico de Vendas por Plataforma
    const plataformaCtx = document.getElementById('vendasPlataformaChart').getContext('2d');
    const vendasPorPlataforma = processarVendasPorPlataforma(vendasData);

    new Chart(plataformaCtx, {
      type: 'doughnut',
      data: {
        labels: vendasPorPlataforma.labels,
        datasets: [{
          data: vendasPorPlataforma.valores,
          backgroundColor: ['#018820', '#f59e0b', '#3b82f6', '#ef4444'],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });

    // Funções auxiliares
    function processarVendasPorMes(vendas) {
      const meses = {};

      vendas.forEach(venda => {
        const data = new Date(venda.data);
        const mesAno = `${data.getMonth() + 1}/${data.getFullYear()}`;

        if (!meses[mesAno]) {
          meses[mesAno] = { vendas: 0, faturamento: 0 };
        }

        meses[mesAno].vendas += 1;
        meses[mesAno].faturamento += parseFloat(venda.valor_total || 0);
      });

      const labels = Object.keys(meses).sort();
      const vendasArray = labels.map(mes => meses[mes].vendas);
      const faturamentoArray = labels.map(mes => meses[mes].faturamento);

      return {
        labels: labels,
        vendas: vendasArray,
        faturamento: faturamentoArray
      };
    }

    function processarVendasPorPlataforma(vendas) {
      const plataformas = {};

      vendas.forEach(venda => {
        const plataforma = venda.plataforma_nome || 'Não informado';

        if (!plataformas[plataforma]) {
          plataformas[plataforma] = 0;
        }

        plataformas[plataforma] += parseFloat(venda.valor_total || 0);
      });

      return {
        labels: Object.keys(plataformas),
        valores: Object.values(plataformas)
      };
    }

    // Função de sincronização
    async function sincronizarPlataformas() {
      const button = document.getElementById('syncButton');
      const status = document.getElementById('syncStatus');

      // Desabilitar botão
      button.disabled = true;
      button.innerHTML = '🔄 Sincronizando...';

      // Mostrar status
      status.style.display = 'inline-flex';
      status.className = 'status-indicator status-warning';
      status.innerHTML = '⏳ Sincronizando...';

      try {
        // Simular chamada para API de sincronização
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Sucesso
        status.className = 'status-indicator status-success';
        status.innerHTML = '✅ Sincronizado com sucesso!';

        // Recarregar página após 2 segundos
        setTimeout(() => {
          window.location.reload();
        }, 2000);

      } catch (error) {
        // Erro
        status.className = 'status-indicator status-error';
        status.innerHTML = '❌ Erro na sincronização';

        // Reabilitar botão
        button.disabled = false;
        button.innerHTML = '🔄 Sincronizar Plataformas';
      }
    }

    // Função de filtro
    function filtrarVendas() {
      const dataInicio = document.getElementById('dataInicio').value;
      const dataFim = document.getElementById('dataFim').value;
      const plataforma = document.getElementById('plataformaFiltro').value;

      console.log('Filtrando vendas:', { dataInicio, dataFim, plataforma });

      // Aqui implementaria a lógica real de filtro
      alert(`Filtro aplicado:\nPeríodo: ${dataInicio} a ${dataFim}\nPlataforma: ${plataforma || 'Todas'}`);
    }

    // Auto-sincronização a cada 5 minutos
    setInterval(() => {
      console.log('🔄 Auto-sincronização executada');
      // Aqui implementaria a sincronização automática silenciosa
    }, 5 * 60 * 1000);

    console.log('📊 Página de vendas carregada com sucesso');
  </script>
</body>
</html>
