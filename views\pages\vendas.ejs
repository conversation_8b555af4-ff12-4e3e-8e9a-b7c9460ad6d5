<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/dashboard.css">
</head>
<body>
  <%- include('../components/sidebar') %>
  
  <div class="main-content">
    <%- include('../components/topbar') %>
    
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>Vendas</h1>
        <p>Gestão e acompanhamento de vendas</p>
      </div>
      
      <!-- Estatísticas de vendas -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-info">
            <h3><%= vendas.length %></h3>
            <p>Total de Vendas</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📈</div>
          <div class="stat-info">
            <h3>R$ <%= vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0).toFixed(2) %></h3>
            <p>Faturamento Total</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <h3><%= vendas.reduce((total, v) => total + v.quantidade, 0) %></h3>
            <p>Produtos Vendidos</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🎯</div>
          <div class="stat-info">
            <h3>R$ <%= vendas.length > 0 ? (vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0) / vendas.length).toFixed(2) : '0.00' %></h3>
            <p>Ticket Médio</p>
          </div>
        </div>
      </div>
      
      <!-- Ações e filtros -->
      <div class="action-bar">
        <button class="btn btn-primary" onclick="openAddSaleModal()">
          ➕ Nova Venda
        </button>
        <div class="search-filters">
          <input type="date" id="dateFrom" placeholder="Data inicial">
          <input type="date" id="dateTo" placeholder="Data final">
          <select id="platformFilter">
            <option value="">Todas as plataformas</option>
            <% plataformas.forEach(plataforma => { %>
              <option value="<%= plataforma.id_plataforma %>"><%= plataforma.nome %></option>
            <% }) %>
          </select>
          <button class="btn btn-secondary" onclick="applyFilters()">Filtrar</button>
        </div>
      </div>
      
      <!-- Gráfico de vendas por plataforma -->
      <div class="dashboard-grid">
        <div class="dashboard-card">
          <h3>Vendas por Plataforma</h3>
          <div class="chart-container">
            <% plataformas.forEach(plataforma => { %>
              <% const vendasPlataforma = vendas.filter(v => v.plataforma_nome === plataforma.nome) %>
              <% const valorTotal = vendasPlataforma.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0) %>
              <% const maxValor = Math.max(...plataformas.map(p => {
                const vendas_p = vendas.filter(v => v.plataforma_nome === p.nome);
                return vendas_p.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0);
              })) %>
              <div class="chart-item">
                <span class="platform-name"><%= plataforma.nome %></span>
                <div class="chart-bar">
                  <div class="bar-fill" style="width: <%= maxValor > 0 ? (valorTotal / maxValor) * 100 : 0 %>%"></div>
                </div>
                <span class="platform-value">R$ <%= valorTotal.toFixed(2) %></span>
              </div>
            <% }) %>
          </div>
        </div>
        
        <!-- Produtos mais vendidos -->
        <div class="dashboard-card">
          <h3>Produtos Mais Vendidos</h3>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>Produto</th>
                  <th>Quantidade</th>
                  <th>Valor Total</th>
                </tr>
              </thead>
              <tbody>
                <% 
                  const produtoVendas = {};
                  vendas.forEach(venda => {
                    if (!produtoVendas[venda.produto_nome]) {
                      produtoVendas[venda.produto_nome] = { quantidade: 0, valor: 0 };
                    }
                    produtoVendas[venda.produto_nome].quantidade += venda.quantidade;
                    produtoVendas[venda.produto_nome].valor += parseFloat(venda.valor_total || 0);
                  });
                  const topProdutos = Object.entries(produtoVendas)
                    .sort((a, b) => b[1].quantidade - a[1].quantidade)
                    .slice(0, 5);
                %>
                <% topProdutos.forEach(([produto, dados]) => { %>
                  <tr>
                    <td><%= produto %></td>
                    <td><%= dados.quantidade %></td>
                    <td>R$ <%= dados.valor.toFixed(2) %></td>
                  </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- Lista de vendas -->
      <div class="dashboard-card">
        <h3>Histórico de Vendas</h3>
        <div class="table-container">
          <table id="salesTable">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Plataforma</th>
                <th>Quantidade</th>
                <th>Valor Total</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% vendas.forEach(venda => { %>
                <tr>
                  <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                  <td><%= venda.produto_nome %></td>
                  <td><%= venda.plataforma_nome %></td>
                  <td><%= venda.quantidade %></td>
                  <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-icon" onclick="editSale(<%= venda.id_venda %>)" title="Editar">
                        ✏️
                      </button>
                      <button class="btn-icon" onclick="viewSale(<%= venda.id_venda %>)" title="Visualizar">
                        👁️
                      </button>
                      <button class="btn-icon delete" onclick="deleteSale(<%= venda.id_venda %>)" title="Excluir">
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para nova venda -->
  <div id="saleModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="saleModalTitle">Nova Venda</h3>
        <span class="close" onclick="closeSaleModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="saleForm">
          <div class="form-group">
            <label for="saleProduct">Produto:</label>
            <select id="saleProduct" name="id_produto" required>
              <option value="">Selecione um produto</option>
              <% produtos.forEach(produto => { %>
                <option value="<%= produto.id_produto %>"><%= produto.nome %> (Estoque: <%= produto.estoque_atual %>)</option>
              <% }) %>
            </select>
          </div>
          
          <div class="form-group">
            <label for="salePlatform">Plataforma:</label>
            <select id="salePlatform" name="id_plataforma" required>
              <option value="">Selecione uma plataforma</option>
              <% plataformas.forEach(plataforma => { %>
                <option value="<%= plataforma.id_plataforma %>"><%= plataforma.nome %></option>
              <% }) %>
            </select>
          </div>
          
          <div class="form-group">
            <label for="saleQuantity">Quantidade:</label>
            <input type="number" id="saleQuantity" name="quantidade" required min="1">
          </div>
          
          <div class="form-group">
            <label for="saleDate">Data da Venda:</label>
            <input type="date" id="saleDate" name="data" required>
          </div>
          
          <div class="form-group">
            <label for="saleValue">Valor Total:</label>
            <input type="number" id="saleValue" name="valor_total" step="0.01" required>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="closeSaleModal()">
              Cancelar
            </button>
            <button type="submit" class="btn btn-primary">
              Registrar Venda
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script>
    // Definir data de hoje como padrão
    document.getElementById('saleDate').value = new Date().toISOString().split('T')[0];
    
    function openAddSaleModal() {
      document.getElementById('saleModalTitle').textContent = 'Nova Venda';
      document.getElementById('saleForm').reset();
      document.getElementById('saleDate').value = new Date().toISOString().split('T')[0];
      document.getElementById('saleModal').style.display = 'block';
    }
    
    function closeSaleModal() {
      document.getElementById('saleModal').style.display = 'none';
    }
    
    function editSale(id) {
      alert('Editar venda ID: ' + id);
    }
    
    function viewSale(id) {
      alert('Visualizar venda ID: ' + id);
    }
    
    function deleteSale(id) {
      if (confirm('Tem certeza que deseja excluir esta venda?')) {
        alert('Excluir venda ID: ' + id);
      }
    }
    
    function applyFilters() {
      const dateFrom = document.getElementById('dateFrom').value;
      const dateTo = document.getElementById('dateTo').value;
      const platform = document.getElementById('platformFilter').value;
      
      // Implementar filtros
      console.log('Filtros:', { dateFrom, dateTo, platform });
    }
    
    // Calcular valor total automaticamente
    document.getElementById('saleProduct').addEventListener('change', function() {
      // Buscar preço do produto e calcular valor total
    });
    
    document.getElementById('saleQuantity').addEventListener('input', function() {
      // Recalcular valor total
    });
    
    // Fechar modal ao clicar fora
    window.onclick = function(event) {
      const modal = document.getElementById('saleModal');
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    }
    
    // Form submit
    document.getElementById('saleForm').addEventListener('submit', function(e) {
      e.preventDefault();
      // Implementar registro de venda via API
      alert('Venda registrada com sucesso!');
      closeSaleModal();
    });
  </script>
</body>
</html>
