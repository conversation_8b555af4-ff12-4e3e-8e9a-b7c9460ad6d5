<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-final.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- Header horizontal (Topo) com logo e menu -->
  <%- include('../components/header-horizontal') %>
  
  <!-- Layout principal -->
  <div class="main-layout">
    <!-- Área central para o conteúdo principal -->
    <main class="content-area">
      <!-- 4️⃣ Tela de Vendas -->
      <h1 class="page-title">Vendas</h1>
      
      <!-- Bo<PERSON><PERSON><PERSON> de filtro (ex.: "Este mês", "Quantidade $") -->
      <div style="display: flex; gap: 15px; margin-bottom: 30px;">
        <button class="btn btn-primary">Este mês</button>
        <button class="btn btn-secondary">Quantidade $</button>
        <button class="btn btn-secondary">Últimos 7 dias</button>
        <button class="btn btn-secondary">Exportar</button>
      </div>
      
      <!-- Gráfico de vendas: Gráfico de linha ou barra -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📈 Gráfico de Vendas</h3>
        </div>
        <div class="section-content">
          <div class="chart-container">
            <canvas id="vendasChart"></canvas>
          </div>
        </div>
      </div>
      
      <!-- Histórico de vendas: Bloco com tabela ou lista -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📋 Histórico de Vendas</h3>
        </div>
        <div class="section-content">
          <table class="data-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Quantidade</th>
                <th>Valor</th>
                <th>Plataforma</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% if (vendas && vendas.length > 0) { %>
                <% vendas.forEach(venda => { %>
                  <tr>
                    <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    <td><%= venda.produto_nome || 'N/A' %></td>
                    <td><%= venda.quantidade %></td>
                    <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                    <td><%= venda.plataforma_nome || 'N/A' %></td>
                    <td><span style="color: #16a34a; font-weight: bold;">✅ Concluída</span></td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="6" style="text-align: center; color: #666; padding: 40px;">
                    Nenhuma venda encontrada
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Projeção de vendas (próxima semana): Bloco com gráfico ou tabela -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">🔮 Projeção de Vendas (Próxima Semana)</h3>
        </div>
        <div class="section-content">
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #018820;">R$ 15.500</div>
              <div style="color: #666; font-size: 14px;">Vendas Previstas</div>
            </div>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #3b82f6;">45</div>
              <div style="color: #666; font-size: 14px;">Pedidos Esperados</div>
            </div>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #f59e0b;">+12%</div>
              <div style="color: #666; font-size: 14px;">Crescimento</div>
            </div>
          </div>
        </div>
      </div>
      
    </main>
    
    <!-- Lateral direita com Tarefas, Calendário e Pergunte para IA -->
    <%- include('../components/sidebar-right') %>
  </div>

  <script>
    // Gráfico de vendas
    const vendasCtx = document.getElementById('vendasChart').getContext('2d');
    
    new Chart(vendasCtx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul'],
        datasets: [{
          label: 'Vendas (R$)',
          data: [12000, 19000, 8000, 15000, 22000, 18000, 25000],
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  </script>
</body>
</html>
