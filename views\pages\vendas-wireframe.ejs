<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-final.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- Header horizontal (Topo) com logo e menu -->
  <%- include('../components/header-horizontal') %>

  <!-- Layout principal -->
  <div class="main-layout">
    <!-- Área central para o conteúdo principal -->
    <main class="content-area">
      <!-- 4️⃣ Tela de Vendas -->
      <h1 class="page-title">Vendas</h1>

      <!-- Cards de métricas rápidas -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
        <div class="summary-card">
          <div class="card-icon">💰</div>
          <div class="card-title">Total de Vendas</div>
          <div class="card-value">R$ <%= vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ".") %></div>
        </div>

        <div class="summary-card">
          <div class="card-icon" style="background: #3b82f6;">📊</div>
          <div class="card-title">Vendas Hoje</div>
          <div class="card-value"><%= vendas.filter(v => new Date(v.data).toDateString() === new Date().toDateString()).length %></div>
        </div>

        <div class="summary-card">
          <div class="card-icon" style="background: #f59e0b;">📈</div>
          <div class="card-title">Crescimento</div>
          <div class="card-value">+12%</div>
        </div>

        <div class="summary-card">
          <div class="card-icon" style="background: #8b5cf6;">🎯</div>
          <div class="card-title">Meta Mensal</div>
          <div class="card-value">78%</div>
        </div>
      </div>

      <!-- Botões de filtro (ex.: "Este mês", "Quantidade $") -->
      <div style="display: flex; gap: 15px; margin-bottom: 30px;">
        <button class="btn btn-primary">Este mês</button>
        <button class="btn btn-secondary">Quantidade $</button>
        <button class="btn btn-secondary">Últimos 7 dias</button>
        <button class="btn btn-secondary" onclick="sincronizarPlataformas()">🔄 Sincronizar</button>
      </div>

      <!-- Gráfico de vendas: Gráfico de linha ou barra -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📈 Gráfico de Vendas</h3>
        </div>
        <div class="section-content">
          <div class="chart-container">
            <canvas id="vendasChart"></canvas>
          </div>
        </div>
      </div>

      <!-- Lista de Vendas com Design Melhorado (MANTENDO O DESIGN ANTERIOR QUE ESTAVA BOM) -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📋 Histórico de Vendas</h3>
          <div style="display: flex; gap: 10px;">
            <button class="btn btn-secondary" onclick="exportarVendas()">📄 Exportar</button>
            <button class="btn btn-primary" onclick="novaVenda()">+ Nova Venda</button>
          </div>
        </div>
        <div class="section-content">
          <% if (vendas && vendas.length > 0) { %>
            <div style="display: grid; gap: 15px;">
              <% vendas.forEach((venda, index) => { %>
                <div style="background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 12px; padding: 20px; transition: all 0.2s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                      <div style="width: 50px; height: 50px; border-radius: 12px; background: linear-gradient(135deg, #018820, #02a025); display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; font-weight: bold;">
                        #<%= index + 1 %>
                      </div>
                      <div>
                        <h4 style="margin: 0; color: #333; font-size: 16px;"><%= venda.produto_nome || 'Produto N/A' %></h4>
                        <p style="margin: 0; color: #666; font-size: 14px;">
                          <%= new Date(venda.data).toLocaleDateString('pt-BR') %> •
                          <%= venda.plataforma_nome || 'Plataforma N/A' %>
                        </p>
                      </div>
                    </div>
                    <div style="text-align: right;">
                      <div style="font-size: 20px; font-weight: bold; color: #018820; margin-bottom: 5px;">
                        R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %>
                      </div>
                      <div style="display: flex; align-items: center; gap: 10px;">
                        <span style="background: #dcfce7; color: #16a34a; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                          ✅ Concluída
                        </span>
                        <button class="btn btn-secondary" onclick="verDetalhesVenda(<%= venda.id_venda || index %>)" style="padding: 6px 12px; font-size: 12px;">
                          👁️ Ver
                        </button>
                      </div>
                    </div>
                  </div>

                  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
                    <div>
                      <label style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Quantidade</label>
                      <div style="font-size: 16px; font-weight: bold; color: #333;"><%= venda.quantidade %> unidades</div>
                    </div>
                    <div>
                      <label style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Valor Unitário</label>
                      <div style="font-size: 16px; font-weight: bold; color: #333;">R$ <%= (parseFloat(venda.valor_total || 0) / venda.quantidade).toFixed(2) %></div>
                    </div>
                    <div>
                      <label style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Comissão</label>
                      <div style="font-size: 16px; font-weight: bold; color: #f59e0b;">R$ <%= (parseFloat(venda.valor_total || 0) * 0.05).toFixed(2) %></div>
                    </div>
                    <div>
                      <label style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Lucro</label>
                      <div style="font-size: 16px; font-weight: bold; color: #16a34a;">R$ <%= (parseFloat(venda.valor_total || 0) * 0.3).toFixed(2) %></div>
                    </div>
                  </div>
                </div>
              <% }) %>
            </div>

            <!-- Paginação -->
            <div style="display: flex; justify-content: center; align-items: center; gap: 15px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
              <button class="btn btn-secondary" onclick="paginaAnterior()" style="padding: 8px 16px;">
                ← Anterior
              </button>
              <span style="color: #666; font-size: 14px;">
                Página 1 de 1 • <%= vendas.length %> vendas
              </span>
              <button class="btn btn-secondary" onclick="proximaPagina()" style="padding: 8px 16px;">
                Próxima →
              </button>
            </div>
          <% } else { %>
            <div style="text-align: center; color: #666; padding: 60px 20px;">
              <div style="font-size: 64px; margin-bottom: 20px;">💰</div>
              <h3 style="margin: 0 0 10px; color: #333;">Nenhuma venda encontrada</h3>
              <p style="margin: 0 0 30px;">Suas vendas aparecerão aqui após a sincronização com as plataformas</p>
              <button class="btn btn-primary" onclick="sincronizarPlataformas()">
                🔄 Sincronizar Agora
              </button>
            </div>
          <% } %>
        </div>
      </div>

      <!-- Projeção de vendas (próxima semana): Bloco com gráfico ou tabela -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">🔮 Projeção de Vendas (Próxima Semana)</h3>
        </div>
        <div class="section-content">
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #018820;">R$ 15.500</div>
              <div style="color: #666; font-size: 14px;">Vendas Previstas</div>
            </div>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #3b82f6;">45</div>
              <div style="color: #666; font-size: 14px;">Pedidos Esperados</div>
            </div>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #f59e0b;">+12%</div>
              <div style="color: #666; font-size: 14px;">Crescimento</div>
            </div>
          </div>
        </div>
      </div>

    </main>

    <!-- Lateral direita com Tarefas, Calendário e Pergunte para IA -->
    <%- include('../components/sidebar-right') %>
  </div>

  <script>
    // Gráfico de vendas
    const vendasCtx = document.getElementById('vendasChart').getContext('2d');

    new Chart(vendasCtx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul'],
        datasets: [{
          label: 'Vendas (R$)',
          data: [12000, 19000, 8000, 15000, 22000, 18000, 25000],
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });

    // Funções para a listagem melhorada (MANTENDO AS FUNÇÕES QUE ESTAVAM BOAS)
    function exportarVendas() {
      alert('📄 Exportando vendas...\n\nEm breve você receberá um arquivo Excel com todas as vendas.');
    }

    function novaVenda() {
      alert('🚧 Funcionalidade em desenvolvimento\n\nEm breve você poderá registrar vendas manuais.');
    }

    function verDetalhesVenda(id) {
      alert(`👁️ Detalhes da Venda #${id}\n\nEsta funcionalidade abrirá uma modal com todos os detalhes da venda.`);
    }

    function paginaAnterior() {
      alert('← Página anterior\n\nFuncionalidade de paginação em desenvolvimento.');
    }

    function proximaPagina() {
      alert('→ Próxima página\n\nFuncionalidade de paginação em desenvolvimento.');
    }

    function sincronizarPlataformas() {
      alert('🔄 Sincronizando plataformas...\n\nBuscando novas vendas do Mercado Livre e Shopee.');
      // Simular sincronização
      setTimeout(() => {
        alert('✅ Sincronização concluída!\n\n3 novas vendas encontradas.');
        location.reload();
      }, 2000);
    }

    console.log('📊 Página de vendas carregada com design melhorado');
  </script>
</body>
</html>
