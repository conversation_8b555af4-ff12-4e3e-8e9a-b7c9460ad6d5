<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/dashboard.css">
</head>
<body>
  <%- include('../components/sidebar') %>
  
  <div class="main-content">
    <%- include('../components/topbar') %>
    
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>Controle de Estoque</h1>
        <p>Monitoramento e gestão de estoque</p>
      </div>
      
      <!-- Cards de alertas -->
      <div class="stats-grid">
        <div class="stat-card alert">
          <div class="stat-icon">⚠️</div>
          <div class="stat-info">
            <h3><%= produtosEstoqueBaixo.length %></h3>
            <p>Produtos com Estoque Baixo</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📦</div>
          <div class="stat-info">
            <h3><%= produtos.length %></h3>
            <p>Total de Produtos</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <h3><%= produtos.reduce((total, p) => total + p.estoque_atual, 0) %></h3>
            <p>Total em Estoque</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-info">
            <h3>R$ <%= produtos.reduce((total, p) => total + (p.estoque_atual * parseFloat(p.preco)), 0).toFixed(2) %></h3>
            <p>Valor Total do Estoque</p>
          </div>
        </div>
      </div>
      
      <!-- Produtos com estoque baixo -->
      <% if (produtosEstoqueBaixo.length > 0) { %>
        <div class="dashboard-card alert-card">
          <h3>🚨 Produtos com Estoque Baixo (≤ 10 unidades)</h3>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>SKU</th>
                  <th>Produto</th>
                  <th>Estoque Atual</th>
                  <th>Preço</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                <% produtosEstoqueBaixo.forEach(produto => { %>
                  <tr class="low-stock-row">
                    <td><%= produto.sku %></td>
                    <td><%= produto.nome %></td>
                    <td class="low-stock"><%= produto.estoque_atual %></td>
                    <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                    <td>
                      <button class="btn btn-primary btn-sm" onclick="updateStock(<%= produto.id_produto %>)">
                        Atualizar Estoque
                      </button>
                    </td>
                  </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        </div>
      <% } %>
      
      <!-- Controle de estoque geral -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>Controle de Estoque Geral</h3>
          <div class="card-actions">
            <button class="btn btn-secondary" onclick="exportStock()">
              📊 Exportar Relatório
            </button>
            <button class="btn btn-primary" onclick="openBulkUpdateModal()">
              📝 Atualização em Lote
            </button>
          </div>
        </div>
        
        <div class="filters-bar">
          <input type="text" placeholder="Buscar produtos..." id="stockSearch">
          <select id="stockFilter">
            <option value="">Todos os produtos</option>
            <option value="low">Estoque baixo</option>
            <option value="normal">Estoque normal</option>
            <option value="high">Estoque alto</option>
          </select>
        </div>
        
        <div class="table-container">
          <table id="stockTable">
            <thead>
              <tr>
                <th>SKU</th>
                <th>Produto</th>
                <th>Estoque Atual</th>
                <th>Valor Unitário</th>
                <th>Valor Total</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% produtos.forEach(produto => { %>
                <tr>
                  <td><%= produto.sku %></td>
                  <td><%= produto.nome %></td>
                  <td class="<%= produto.estoque_atual <= 10 ? 'low-stock' : produto.estoque_atual <= 50 ? 'medium-stock' : 'high-stock' %>">
                    <%= produto.estoque_atual %>
                  </td>
                  <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                  <td>R$ <%= (produto.estoque_atual * parseFloat(produto.preco)).toFixed(2) %></td>
                  <td>
                    <span class="stock-status <%= produto.estoque_atual <= 10 ? 'low' : produto.estoque_atual <= 50 ? 'medium' : 'high' %>">
                      <%= produto.estoque_atual <= 10 ? 'Baixo' : produto.estoque_atual <= 50 ? 'Normal' : 'Alto' %>
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-icon" onclick="updateStock(<%= produto.id_produto %>)" title="Atualizar Estoque">
                        📝
                      </button>
                      <button class="btn-icon" onclick="viewStockHistory(<%= produto.id_produto %>)" title="Histórico">
                        📊
                      </button>
                    </div>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para atualizar estoque -->
  <div id="stockModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Atualizar Estoque</h3>
        <span class="close" onclick="closeStockModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="stockForm">
          <div class="form-group">
            <label for="currentStock">Estoque Atual:</label>
            <input type="number" id="currentStock" readonly>
          </div>
          
          <div class="form-group">
            <label for="newStock">Novo Estoque:</label>
            <input type="number" id="newStock" name="estoque_atual" required min="0">
          </div>
          
          <div class="form-group">
            <label for="stockReason">Motivo da Alteração:</label>
            <select id="stockReason" name="motivo">
              <option value="entrada">Entrada de Mercadoria</option>
              <option value="saida">Saída/Venda</option>
              <option value="ajuste">Ajuste de Inventário</option>
              <option value="perda">Perda/Avaria</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="stockNotes">Observações:</label>
            <textarea id="stockNotes" name="observacoes" rows="3"></textarea>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="closeStockModal()">
              Cancelar
            </button>
            <button type="submit" class="btn btn-primary">
              Atualizar Estoque
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <style>
    .alert-card {
      border-left: 4px solid #e74c3c;
      background-color: #fdf2f2;
    }
    
    .stat-card.alert {
      border-left: 4px solid #e74c3c;
    }
    
    .low-stock-row {
      background-color: #fdf2f2;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .card-actions {
      display: flex;
      gap: 10px;
    }
    
    .filters-bar {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 5px;
    }
    
    .filters-bar input,
    .filters-bar select {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    
    .stock-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
    }
    
    .stock-status.low {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    .stock-status.medium {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .stock-status.high {
      background-color: #d4edda;
      color: #155724;
    }
    
    .medium-stock {
      color: #856404;
      font-weight: bold;
    }
    
    .high-stock {
      color: #155724;
      font-weight: bold;
    }
    
    .btn-sm {
      padding: 5px 10px;
      font-size: 12px;
    }
    
    textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-family: inherit;
      resize: vertical;
    }
    
    textarea:focus {
      outline: none;
      border-color: #018820;
    }
  </style>

  <script>
    let currentProductId = null;
    
    function updateStock(productId) {
      currentProductId = productId;
      // Aqui você buscaria os dados do produto
      document.getElementById('currentStock').value = '10'; // Exemplo
      document.getElementById('newStock').value = '';
      document.getElementById('stockModal').style.display = 'block';
    }
    
    function closeStockModal() {
      document.getElementById('stockModal').style.display = 'none';
      currentProductId = null;
    }
    
    function viewStockHistory(productId) {
      alert('Visualizar histórico do produto ID: ' + productId);
    }
    
    function exportStock() {
      alert('Exportar relatório de estoque');
    }
    
    function openBulkUpdateModal() {
      alert('Atualização em lote');
    }
    
    // Fechar modal ao clicar fora
    window.onclick = function(event) {
      const modal = document.getElementById('stockModal');
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    }
    
    // Filtros
    document.getElementById('stockSearch').addEventListener('input', function() {
      // Implementar busca
    });
    
    document.getElementById('stockFilter').addEventListener('change', function() {
      // Implementar filtro
    });
    
    // Form submit
    document.getElementById('stockForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (currentProductId) {
        // Implementar atualização via API
        alert('Estoque atualizado para produto ID: ' + currentProductId);
        closeStockModal();
      }
    });
  </script>
</body>
</html>
