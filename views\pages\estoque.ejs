<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-final.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- Header horizontal (Topo) com logo e menu -->
  <%- include('../components/header-horizontal') %>
  
  <!-- Layout principal -->
  <div class="main-layout">
    <!-- Área central para o conteúdo principal -->
    <main class="content-area">
      <!-- 5️⃣ Tela de Estoque -->
      <h1 class="page-title">Estoque</h1>
      
      <!-- Gráficos circulares: "Em estoque", "Quantidade de compras", "A enviar" -->
      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin-bottom: 40px;">
        
        <!-- Gráfico "Em estoque" -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">📦 Em estoque</h3>
          </div>
          <div class="section-content">
            <div style="position: relative; height: 250px;">
              <canvas id="estoqueChart"></canvas>
            </div>
            <div style="text-align: center; margin-top: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: #018820;">
                <%= produtos.reduce((total, p) => total + p.estoque_atual, 0) %>
              </div>
              <div style="color: #666; font-size: 14px;">Unidades totais</div>
            </div>
          </div>
        </div>
        
        <!-- Gráfico "Quantidade de compras" -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🛒 Quantidade de compras</h3>
          </div>
          <div class="section-content">
            <div style="position: relative; height: 250px;">
              <canvas id="comprasChart"></canvas>
            </div>
            <div style="text-align: center; margin-top: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: #3b82f6;">R$ 45.200</div>
              <div style="color: #666; font-size: 14px;">Valor em compras</div>
            </div>
          </div>
        </div>
        
        <!-- Gráfico "A enviar" -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🚚 A enviar</h3>
          </div>
          <div class="section-content">
            <div style="position: relative; height: 250px;">
              <canvas id="enviarChart"></canvas>
            </div>
            <div style="text-align: center; margin-top: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: #f59e0b;">28</div>
              <div style="color: #666; font-size: 14px;">Pedidos pendentes</div>
            </div>
          </div>
        </div>
        
      </div>
      
      <!-- Lista detalhada de produtos -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📋 Produtos em Estoque</h3>
          <button class="btn btn-primary">+ Adicionar Produto</button>
        </div>
        <div class="section-content">
          <table class="data-table">
            <thead>
              <tr>
                <th>Produto</th>
                <th>SKU</th>
                <th>Estoque Atual</th>
                <th>Estoque Mínimo</th>
                <th>Preço</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (produtos && produtos.length > 0) { %>
                <% produtos.forEach(produto => { %>
                  <tr>
                    <td><strong><%= produto.nome %></strong></td>
                    <td><%= produto.sku %></td>
                    <td>
                      <% if (produto.estoque_atual <= 10) { %>
                        <span style="color: #ef4444; font-weight: bold;"><%= produto.estoque_atual %></span>
                      <% } else { %>
                        <span style="color: #16a34a;"><%= produto.estoque_atual %></span>
                      <% } %>
                    </td>
                    <td>10</td>
                    <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                    <td>
                      <% if (produto.estoque_atual <= 10) { %>
                        <span style="background: #fee2e2; color: #dc2626; padding: 4px 8px; border-radius: 4px; font-size: 12px;">⚠️ Baixo</span>
                      <% } else { %>
                        <span style="background: #dcfce7; color: #16a34a; padding: 4px 8px; border-radius: 4px; font-size: 12px;">✅ Normal</span>
                      <% } %>
                    </td>
                    <td>
                      <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">Editar</button>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="7" style="text-align: center; color: #666; padding: 40px;">
                    Nenhum produto encontrado
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
      
    </main>
    
    <!-- Lateral direita com Tarefas, Calendário e Pergunte para IA -->
    <%- include('../components/sidebar-right') %>
  </div>

  <script>
    // Gráfico "Em estoque"
    const estoqueCtx = document.getElementById('estoqueChart').getContext('2d');
    new Chart(estoqueCtx, {
      type: 'doughnut',
      data: {
        labels: ['Normal', 'Baixo', 'Crítico'],
        datasets: [{
          data: [65, 25, 10],
          backgroundColor: ['#16a34a', '#f59e0b', '#ef4444'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });

    // Gráfico "Quantidade de compras"
    const comprasCtx = document.getElementById('comprasChart').getContext('2d');
    new Chart(comprasCtx, {
      type: 'doughnut',
      data: {
        labels: ['Realizadas', 'Pendentes', 'Canceladas'],
        datasets: [{
          data: [70, 20, 10],
          backgroundColor: ['#3b82f6', '#f59e0b', '#ef4444'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });

    // Gráfico "A enviar"
    const enviarCtx = document.getElementById('enviarChart').getContext('2d');
    new Chart(enviarCtx, {
      type: 'doughnut',
      data: {
        labels: ['Preparando', 'Pronto', 'Enviado'],
        datasets: [{
          data: [40, 35, 25],
          backgroundColor: ['#f59e0b', '#8b5cf6', '#16a34a'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });
  </script>
</body>
</html>
