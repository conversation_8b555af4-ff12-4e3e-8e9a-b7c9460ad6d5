<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-correct.css">
</head>
<body>
  <div class="app-container">
    <%- include('../components/navbar') %>

    <main class="main-content">
      <div class="page-header">
        <h1 class="page-title">Estoque</h1>
        <p class="page-subtitle">Controle inteligente de inventário</p>
      </div>

      <div class="content-area">
        <!-- Cards de Métricas -->
        <div class="metrics-grid">
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📦</div>
              <h3 class="card-title">Total em Estoque</h3>
            </div>
            <div class="card-value"><%= produtos.reduce((total, p) => total + p.estoque_atual, 0) %></div>
            <div class="card-subtitle">unidades totais</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">⚠️</div>
              <h3 class="card-title">Estoque Baixo</h3>
            </div>
            <div class="card-value"><%= produtos.filter(p => p.estoque_atual <= 10).length %></div>
            <div class="card-subtitle">produtos críticos</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">🚨</div>
              <h3 class="card-title">Estoque Zero</h3>
            </div>
            <div class="card-value"><%= produtos.filter(p => p.estoque_atual === 0).length %></div>
            <div class="card-subtitle">produtos sem estoque</div>
          </div>

          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">💰</div>
              <h3 class="card-title">Valor Total</h3>
            </div>
            <div class="card-value">R$ <%= produtos.reduce((total, p) => total + (parseFloat(p.preco) * p.estoque_atual), 0).toFixed(2) %></div>
            <div class="card-subtitle">valor em estoque</div>
          </div>
        </div>

        <!-- Tabela de Estoque -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Controle de Estoque</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Produto</th>
                <th>SKU</th>
                <th>Estoque Atual</th>
                <th>Valor Unitário</th>
                <th>Valor Total</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (produtos && produtos.length > 0) { %>
                <% produtos.forEach(produto => { %>
                  <tr>
                    <td><%= produto.nome %></td>
                    <td><%= produto.sku %></td>
                    <td><%= produto.estoque_atual %></td>
                    <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                    <td>R$ <%= (parseFloat(produto.preco) * produto.estoque_atual).toFixed(2) %></td>
                    <td>
                      <% if (produto.estoque_atual === 0) { %>
                        <span style="color: #dc3545;">Sem Estoque</span>
                      <% } else if (produto.estoque_atual <= 10) { %>
                        <span style="color: #ffc107;">Estoque Baixo</span>
                      <% } else { %>
                        <span style="color: #28a745;">Normal</span>
                      <% } %>
                    </td>
                    <td>
                      <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">
                        Atualizar
                      </button>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="7" style="text-align: center; color: #666; padding: 40px;">
                    Nenhum produto encontrado
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
    </main>
  </div>
</body>
</html>
