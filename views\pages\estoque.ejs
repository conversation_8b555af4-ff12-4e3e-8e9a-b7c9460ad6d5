<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe.css">
</head>
<body>
  <div class="app-container">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content -->
    <div class="main-content">
      <!-- Topbar -->
      <%- include('../components/topbar') %>
      
      <!-- Content Area -->
      <div class="content-area">
        <!-- Page Header -->
        <div class="page-header">
          <h1 class="page-title">Controle de Estoque</h1>
          <p class="page-subtitle">Monitoramento e gestão de estoque</p>
        </div>
        
        <!-- Cards de Resumo -->
        <div class="cards-grid">
          <div class="card">
            <div class="card-header">
              <div class="card-icon">📦</div>
              <div>
                <h3 class="card-title">Total em Estoque</h3>
              </div>
            </div>
            <div class="card-value">
              <%= produtos.reduce((total, p) => total + p.estoque_atual, 0) %>
            </div>
            <div class="card-trend">Unidades totais</div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #fff3cd;">⚠️</div>
              <div>
                <h3 class="card-title">Estoque Baixo</h3>
              </div>
            </div>
            <div class="card-value" style="color: #856404;">
              <%= produtos.filter(p => p.estoque_atual <= 10).length %>
            </div>
            <div class="card-trend">Produtos com estoque crítico</div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #f8d7da;">🚨</div>
              <div>
                <h3 class="card-title">Estoque Zero</h3>
              </div>
            </div>
            <div class="card-value" style="color: #721c24;">
              <%= produtos.filter(p => p.estoque_atual === 0).length %>
            </div>
            <div class="card-trend">Produtos sem estoque</div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #d4edda;">💰</div>
              <div>
                <h3 class="card-title">Valor Total</h3>
              </div>
            </div>
            <div class="card-value" style="color: #155724;">
              R$ <%= produtos.reduce((total, p) => total + (parseFloat(p.preco) * p.estoque_atual), 0).toFixed(2) %>
            </div>
            <div class="card-trend">Valor total em estoque</div>
          </div>
        </div>
        
        <!-- Filtros e Ações -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <div style="display: flex; gap: 15px;">
            <select id="stockFilter" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
              <option value="all">Todos os produtos</option>
              <option value="low">Estoque baixo (≤10)</option>
              <option value="zero">Estoque zero</option>
              <option value="normal">Estoque normal</option>
            </select>
            <input 
              type="text" 
              placeholder="Buscar produtos..." 
              style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; width: 300px;"
              id="searchStock"
            >
          </div>
          <button class="btn btn-primary" onclick="openBulkUpdateModal()">
            📦 Atualização em Lote
          </button>
        </div>
        
        <!-- Alertas de Estoque Baixo -->
        <% if (produtos.filter(p => p.estoque_atual <= 10).length > 0) { %>
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h4 style="color: #856404; margin-bottom: 15px;">⚠️ Alertas de Estoque</h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
            <% produtos.filter(p => p.estoque_atual <= 10).forEach(produto => { %>
              <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ffeaa7;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <div>
                    <strong><%= produto.nome %></strong><br>
                    <small style="color: #666;">SKU: <%= produto.sku %></small>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-size: 18px; font-weight: bold; color: #856404;">
                      <%= produto.estoque_atual %>
                    </div>
                    <button 
                      class="btn btn-primary" 
                      style="padding: 4px 8px; font-size: 12px; margin-top: 5px;"
                      onclick="quickRestock(<%= produto.id_produto %>)"
                    >
                      Repor
                    </button>
                  </div>
                </div>
              </div>
            <% }) %>
          </div>
        </div>
        <% } %>
        
        <!-- Tabela de Estoque -->
        <div class="table-container">
          <div class="table-header">
            <h3 class="table-title">📦 Controle de Estoque</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Produto</th>
                <th>SKU</th>
                <th>Estoque Atual</th>
                <th>Valor Unitário</th>
                <th>Valor Total</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody id="stockTableBody">
              <% if (produtos && produtos.length > 0) { %>
                <% produtos.forEach(produto => { %>
                  <tr data-stock="<%= produto.estoque_atual %>">
                    <td>
                      <strong><%= produto.nome %></strong>
                    </td>
                    <td>
                      <code style="background: #f0f0f0; padding: 2px 6px; border-radius: 4px;">
                        <%= produto.sku %>
                      </code>
                    </td>
                    <td>
                      <div style="display: flex; align-items: center; gap: 10px;">
                        <span style="font-size: 18px; font-weight: bold; color: <%= produto.estoque_atual <= 10 ? '#856404' : produto.estoque_atual === 0 ? '#721c24' : '#155724' %>;">
                          <%= produto.estoque_atual %>
                        </span>
                        <span style="color: #666; font-size: 12px;">unidades</span>
                      </div>
                    </td>
                    <td>
                      R$ <%= parseFloat(produto.preco).toFixed(2) %>
                    </td>
                    <td>
                      <strong>R$ <%= (parseFloat(produto.preco) * produto.estoque_atual).toFixed(2) %></strong>
                    </td>
                    <td>
                      <% if (produto.estoque_atual === 0) { %>
                        <span style="background: #f8d7da; color: #721c24; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                          🚨 Sem Estoque
                        </span>
                      <% } else if (produto.estoque_atual <= 10) { %>
                        <span style="background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                          ⚠️ Estoque Baixo
                        </span>
                      <% } else { %>
                        <span style="background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                          ✅ Normal
                        </span>
                      <% } %>
                    </td>
                    <td>
                      <div style="display: flex; gap: 8px;">
                        <button 
                          class="btn btn-primary" 
                          style="padding: 6px 12px; font-size: 12px;"
                          onclick="updateStock(<%= produto.id_produto %>, '<%= produto.nome %>', <%= produto.estoque_atual %>)"
                        >
                          📝 Atualizar
                        </button>
                        <button 
                          class="btn btn-secondary" 
                          style="padding: 6px 12px; font-size: 12px;"
                          onclick="viewHistory(<%= produto.id_produto %>)"
                        >
                          📊 Histórico
                        </button>
                      </div>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="7" style="text-align: center; color: #666; padding: 40px;">
                    <div>
                      <div style="font-size: 48px; margin-bottom: 16px;">📦</div>
                      <div style="font-size: 18px; margin-bottom: 8px;">Nenhum produto encontrado</div>
                      <div style="font-size: 14px; color: #999;">Adicione produtos para controlar o estoque</div>
                    </div>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Filtros de estoque
    document.getElementById('stockFilter').addEventListener('change', function(e) {
      const filter = e.target.value;
      const rows = document.querySelectorAll('#stockTableBody tr');
      
      rows.forEach(row => {
        const stock = parseInt(row.dataset.stock);
        let show = true;
        
        switch(filter) {
          case 'low':
            show = stock <= 10 && stock > 0;
            break;
          case 'zero':
            show = stock === 0;
            break;
          case 'normal':
            show = stock > 10;
            break;
          default:
            show = true;
        }
        
        row.style.display = show ? '' : 'none';
      });
    });
    
    // Busca
    document.getElementById('searchStock').addEventListener('input', function(e) {
      const searchTerm = e.target.value.toLowerCase();
      const rows = document.querySelectorAll('#stockTableBody tr');
      
      rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
      });
    });
    
    // Atualizar estoque
    function updateStock(id, name, currentStock) {
      const newStock = prompt(`Atualizar estoque de "${name}":\nEstoque atual: ${currentStock} unidades\n\nNova quantidade:`);
      
      if (newStock !== null && !isNaN(newStock) && newStock >= 0) {
        // Implementar atualização via API
        alert(`✅ Estoque de "${name}" atualizado para ${newStock} unidades`);
        // window.location.reload();
      }
    }
    
    // Reposição rápida
    function quickRestock(id) {
      const quantity = prompt('Quantidade para reposição:');
      if (quantity !== null && !isNaN(quantity) && quantity > 0) {
        alert(`✅ Reposição de ${quantity} unidades realizada`);
        // Implementar via API
      }
    }
    
    // Histórico
    function viewHistory(id) {
      alert('Visualizar histórico do produto ID: ' + id);
      // Implementar modal de histórico
    }
    
    // Atualização em lote
    function openBulkUpdateModal() {
      alert('Funcionalidade de atualização em lote em desenvolvimento');
      // Implementar modal de atualização em lote
    }
  </script>
</body>
</html>
