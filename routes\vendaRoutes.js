// routes/vendaRoutes.js

const express = require('express');
const router = express.Router();
const vendaController = require('../controllers/vendaController');

// GET /api/vendas - Buscar todas as vendas
router.get('/', vendaController.getAllVendas);

// GET /api/vendas/periodo - Buscar vendas por período
router.get('/periodo', vendaController.getVendasPorPeriodo);

// GET /api/vendas/relatorio - Relatório de vendas
router.get('/relatorio', vendaController.getRelatorioVendas);

// GET /api/vendas/:id - Buscar venda por ID
router.get('/:id', vendaController.getVendaById);

// POST /api/vendas - Criar nova venda
router.post('/', vendaController.createVenda);

// PUT /api/vendas/:id - Atualizar venda
router.put('/:id', vendaController.updateVenda);

// DELETE /api/vendas/:id - Deletar venda
router.delete('/:id', vendaController.deleteVenda);

module.exports = router;
