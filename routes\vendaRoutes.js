// routes/vendaRoutes.js

const express = require('express');
const router = express.Router();
const vendaController = require('../controllers/vendaController');
const pedidoController = require('../controllers/pedidoController');
const usuarioController = require('../controllers/usuarioController');
const integrationController = require('../controllers/integrationController');

// GET /api/vendas - <PERSON>car todas as vendas
router.get('/', vendaController.getAllVendas);

// GET /api/vendas/periodo - Buscar vendas por período
router.get('/periodo', vendaController.getVendasPorPeriodo);

// GET /api/vendas/relatorio - Relatório de vendas
router.get('/relatorio', vendaController.getRelatorioVendas);

// GET /api/vendas/:id - Buscar venda por ID
router.get('/:id', vendaController.getVendaById);

// POST /api/vendas - Criar nova venda
router.post('/', vendaController.createVenda);

// PUT /api/vendas/:id - Atualizar venda
router.put('/:id', vendaController.updateVenda);

// DELETE /api/vendas/:id - Deletar venda
router.delete('/:id', vendaController.deleteVenda);

// Rotas de renderização de páginas
router.get('/vendas', vendaController.renderVendas);
router.get('/plataformas', vendaController.renderPlataformas);
router.get('/pedidos', pedidoController.renderPedidos);
router.get('/perfil', usuarioController.renderPerfil);

// Rotas API para pedidos
router.get('/api/pedidos', pedidoController.getAllPedidos);
router.get('/api/pedidos/:id', pedidoController.getPedidoById);
router.post('/api/pedidos', pedidoController.createPedido);
router.put('/api/pedidos/:id', pedidoController.updatePedido);
router.delete('/api/pedidos/:id', pedidoController.deletePedido);
router.get('/api/pedidos/status/resumo', pedidoController.getPedidosPorStatus);
router.get('/api/pedidos/projecao/compras', pedidoController.getProjecaoCompras);
router.get('/api/pedidos/relatorio/mensal', pedidoController.getRelatorioPedidos);

// Rotas API para usuários
router.get('/api/usuarios', usuarioController.getAllUsuarios);
router.get('/api/usuarios/:id', usuarioController.getUsuarioById);
router.post('/api/usuarios', usuarioController.createUsuario);
router.put('/api/usuarios/:id', usuarioController.updateUsuario);
router.put('/api/usuarios/:id/senha', usuarioController.updatePassword);
router.delete('/api/usuarios/:id', usuarioController.deleteUsuario);

// Rotas API para métodos de pagamento
router.get('/api/usuarios/:id/pagamentos', usuarioController.getMetodosPagamento);
router.post('/api/usuarios/:id/pagamentos', usuarioController.addMetodoPagamento);
router.put('/api/usuarios/:id/pagamentos/:metodoId', usuarioController.updateMetodoPagamento);
router.delete('/api/usuarios/:id/pagamentos/:metodoId', usuarioController.deleteMetodoPagamento);

// Rotas API para integração de plataformas
router.post('/api/integration/sync-all', integrationController.syncAllPlatforms);
router.get('/api/integration/mercadolivre', integrationController.fetchMercadoLivreData);
router.get('/api/integration/shopee', integrationController.fetchShopeeData);
router.get('/api/integration/metrics', integrationController.getConsolidatedMetrics);
router.post('/api/integration/manual-sale', integrationController.processManualSale);
router.get('/api/integration/report', integrationController.getIntegrationReport);
router.get('/api/integration/test-connectivity', integrationController.testApiConnectivity);
router.post('/api/integration/webhook/configure', integrationController.configureWebhook);
router.post('/api/integration/webhook/receive', integrationController.receiveWebhookData);

module.exports = router;
