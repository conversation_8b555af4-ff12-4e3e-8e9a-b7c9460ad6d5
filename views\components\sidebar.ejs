<!-- SIDEBAR EXATA DO WIREFRAME -->
<div class="sidebar">
  <!-- Header da Sidebar -->
  <div class="sidebar-header">
    <img src="/assets/LogoPCR.png" alt="PCR Labor" class="sidebar-logo">
    <h2 class="sidebar-title">PCR Labor</h2>
  </div>

  <!-- Navegação Principal -->
  <nav class="sidebar-nav">
    <ul class="nav-list">
      <li class="nav-item">
        <a href="/dashboard" class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>">
          <span class="nav-icon">📊</span>
          <span class="nav-text">Dashboard</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="/vendas" class="nav-link <%= currentPage === 'vendas' ? 'active' : '' %>">
          <span class="nav-icon">💰</span>
          <span class="nav-text">Vendas</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="/estoque" class="nav-link <%= currentPage === 'estoque' ? 'active' : '' %>">
          <span class="nav-icon">📦</span>
          <span class="nav-text">Estoque</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="/produtos" class="nav-link <%= currentPage === 'produtos' ? 'active' : '' %>">
          <span class="nav-icon">🧪</span>
          <span class="nav-text">Produtos</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="/plataformas" class="nav-link <%= currentPage === 'plataformas' ? 'active' : '' %>">
          <span class="nav-icon">🌐</span>
          <span class="nav-text">Plataformas</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="/pedidos" class="nav-link <%= currentPage === 'pedidos' ? 'active' : '' %>">
          <span class="nav-icon">📋</span>
          <span class="nav-text">Pedidos</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="/perfil" class="nav-link <%= currentPage === 'perfil' ? 'active' : '' %>">
          <span class="nav-icon">👤</span>
          <span class="nav-text">Perfil</span>
        </a>
      </li>
    </ul>
  </nav>

  <!-- Widget de Calendário -->
  <div style="padding: 20px; border-top: 1px solid rgba(255, 255, 255, 0.1);">
    <h4 style="color: #ecf0f1; font-size: 14px; margin-bottom: 15px;">📅 Calendário</h4>
    <div id="miniCalendar" style="background: rgba(255, 255, 255, 0.1); border-radius: 8px; padding: 15px;">
      <div style="text-align: center; color: #ecf0f1; font-size: 12px; margin-bottom: 10px;">
        <strong id="currentMonth"></strong>
      </div>
      <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 2px; font-size: 10px; color: #bdc3c7;">
        <div style="text-align: center; padding: 2px;">D</div>
        <div style="text-align: center; padding: 2px;">S</div>
        <div style="text-align: center; padding: 2px;">T</div>
        <div style="text-align: center; padding: 2px;">Q</div>
        <div style="text-align: center; padding: 2px;">Q</div>
        <div style="text-align: center; padding: 2px;">S</div>
        <div style="text-align: center; padding: 2px;">S</div>
      </div>
      <div id="calendarDays" style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 2px; margin-top: 5px;">
        <!-- Dias serão gerados via JavaScript -->
      </div>
    </div>
  </div>

  <!-- Widget de IA -->
  <div style="padding: 20px; border-top: 1px solid rgba(255, 255, 255, 0.1);">
    <h4 style="color: #ecf0f1; font-size: 14px; margin-bottom: 15px;">🤖 Assistente IA</h4>
    <div style="background: rgba(255, 255, 255, 0.1); border-radius: 8px; padding: 15px;">
      <div style="color: #ecf0f1; font-size: 12px; line-height: 1.4; margin-bottom: 10px;">
        <strong>💡 Sugestão do dia:</strong><br>
        <span id="aiSuggestion">Considere reabastecer o estoque de Kit PCR COVID-19. Vendas aumentaram 15% esta semana.</span>
      </div>
      <button onclick="getNewSuggestion()" style="background: #018820; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 11px; cursor: pointer; width: 100%;">
        Nova Sugestão
      </button>
    </div>
  </div>

  <!-- Widget de Tarefas -->
  <div style="padding: 20px; border-top: 1px solid rgba(255, 255, 255, 0.1);">
    <h4 style="color: #ecf0f1; font-size: 14px; margin-bottom: 15px;">✅ Tarefas</h4>
    <div style="background: rgba(255, 255, 255, 0.1); border-radius: 8px; padding: 15px;">
      <div id="taskList" style="color: #ecf0f1; font-size: 11px;">
        <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
          <input type="checkbox" id="task1" style="margin: 0;">
          <label for="task1">Revisar pedidos pendentes</label>
        </div>
        <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
          <input type="checkbox" id="task2" style="margin: 0;">
          <label for="task2">Sincronizar Mercado Livre</label>
        </div>
        <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
          <input type="checkbox" id="task3" checked style="margin: 0;">
          <label for="task3" style="text-decoration: line-through; opacity: 0.6;">Atualizar preços Shopee</label>
        </div>
      </div>
      <button onclick="addNewTask()" style="background: #018820; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 11px; cursor: pointer; width: 100%; margin-top: 10px;">
        + Nova Tarefa
      </button>
    </div>
  </div>

  <script>
    // Gerar calendário
    function generateCalendar() {
      const now = new Date();
      const currentMonth = now.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
      document.getElementById('currentMonth').textContent = currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1);

      const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - firstDay.getDay());

      const calendarDays = document.getElementById('calendarDays');
      calendarDays.innerHTML = '';

      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);

        const dayElement = document.createElement('div');
        dayElement.style.cssText = 'text-align: center; padding: 4px 2px; font-size: 10px; cursor: pointer; border-radius: 2px;';
        dayElement.textContent = date.getDate();

        if (date.getMonth() === now.getMonth()) {
          dayElement.style.color = '#ecf0f1';
          if (date.getDate() === now.getDate()) {
            dayElement.style.background = '#018820';
            dayElement.style.color = 'white';
          }
        } else {
          dayElement.style.color = '#7f8c8d';
        }

        calendarDays.appendChild(dayElement);
      }
    }

    // Sugestões da IA
    const aiSuggestions = [
      'Considere reabastecer o estoque de Kit PCR COVID-19. Vendas aumentaram 15% esta semana.',
      'Produtos de Hepatite B estão com baixa rotatividade. Considere promoções.',
      'Mercado Livre está com 23% mais vendas que a Shopee este mês.',
      'Estoque de Kit PCR Influenza pode acabar em 5 dias no ritmo atual.',
      'Considere expandir para Amazon. Potencial de 30% mais vendas.',
      'Preços da concorrência subiram 8%. Oportunidade de ajuste.',
      'Clientes preferem compras às terças e quintas. Foque marketing nesses dias.'
    ];

    function getNewSuggestion() {
      const randomSuggestion = aiSuggestions[Math.floor(Math.random() * aiSuggestions.length)];
      document.getElementById('aiSuggestion').textContent = randomSuggestion;
    }

    // Adicionar nova tarefa
    function addNewTask() {
      const taskText = prompt('Digite a nova tarefa:');
      if (taskText) {
        const taskList = document.getElementById('taskList');
        const newTask = document.createElement('div');
        newTask.style.cssText = 'margin-bottom: 8px; display: flex; align-items: center; gap: 8px;';

        const taskId = 'task' + Date.now();
        newTask.innerHTML = `
          <input type="checkbox" id="${taskId}" style="margin: 0;">
          <label for="${taskId}">${taskText}</label>
        `;

        taskList.appendChild(newTask);
      }
    }

    // Inicializar widgets
    document.addEventListener('DOMContentLoaded', function() {
      generateCalendar();

      // Atualizar sugestão da IA a cada 30 segundos
      setInterval(getNewSuggestion, 30000);
    });
  </script>
</div>
