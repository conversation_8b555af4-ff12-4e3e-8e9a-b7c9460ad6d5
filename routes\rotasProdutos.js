// routes/produtoRoutes.js

const express = require('express');
const router = express.Router();
const controladorProdutos = require('../controllers/controladorProdutos');

// GET /api/produtos - Buscar todos os produtos
router.get('/', controladorProdutos.getAllProdutos);

// GET /api/produtos/:id - Buscar produto por ID
router.get('/:id', controladorProdutos.getProdutoById);

// POST /api/produtos - Criar novo produto
router.post('/', controladorProdutos.createProduto);

// PUT /api/produtos/:id - Atualizar produto
router.put('/:id', controladorProdutos.updateProduto);

// PATCH /api/produtos/:id/estoque - Atualizar apenas estoque
router.patch('/:id/estoque', controladorProdutos.updateEstoque);

// DELETE /api/produtos/:id - Deletar produto
router.delete('/:id', controladorProdutos.deleteProduto);

module.exports = router;
