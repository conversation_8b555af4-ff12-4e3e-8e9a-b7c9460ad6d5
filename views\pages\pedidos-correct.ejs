<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-correct.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="app-container">
    <%- include('../components/navbar') %>
    
    <main class="main-content">
      <div class="page-header">
        <h1 class="page-title">Pedidos</h1>
        <p class="page-subtitle">Gestão de pedidos e sugestões automáticas de compra</p>
      </div>
      
      <!-- Métricas de Pedidos -->
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <div class="metric-icon">📋</div>
            <div class="metric-trend trend-up">+5%</div>
          </div>
          <div class="metric-value"><%= typeof stats !== 'undefined' ? stats.totalPedidos : 0 %></div>
          <div class="metric-label">Total de Pedidos</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <div class="metric-icon">⏳</div>
            <div class="metric-trend trend-up">+2</div>
          </div>
          <div class="metric-value"><%= typeof stats !== 'undefined' ? stats.pedidosPendentes : 0 %></div>
          <div class="metric-label">Pendentes</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <div class="metric-icon">✅</div>
            <div class="metric-trend trend-up">+8</div>
          </div>
          <div class="metric-value"><%= typeof stats !== 'undefined' ? stats.pedidosAprovados : 0 %></div>
          <div class="metric-label">Aprovados</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <div class="metric-icon">💰</div>
            <div class="metric-trend trend-up">+12%</div>
          </div>
          <div class="metric-value">R$ <%= typeof stats !== 'undefined' ? stats.valorTotalPedidos.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ".") : '0' %></div>
          <div class="metric-label">Valor Total</div>
        </div>
      </div>
      
      <!-- Sugestões Automáticas -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">🤖 Sugestões Automáticas de Compra</h3>
          <button class="btn btn-primary" onclick="gerarSugestoes()">🔄 Atualizar Sugestões</button>
        </div>
        <div class="section-content">
          <table class="data-table">
            <thead>
              <tr>
                <th>Produto</th>
                <th>Estoque Atual</th>
                <th>Prioridade</th>
                <th>Sugestão</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (typeof projecaoCompras !== 'undefined' && projecaoCompras.length > 0) { %>
                <% projecaoCompras.forEach(item => { %>
                  <tr>
                    <td><strong><%= item.produto_nome %></strong></td>
                    <td>
                      <% if (item.estoque_atual <= 10) { %>
                        <span style="color: #dc2626; font-weight: bold;"><%= item.estoque_atual %></span>
                      <% } else { %>
                        <span><%= item.estoque_atual %></span>
                      <% } %>
                    </td>
                    <td>
                      <% if (item.prioridade === 'URGENTE') { %>
                        <span style="background: #fee2e2; color: #dc2626; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">🚨 URGENTE</span>
                      <% } else if (item.prioridade === 'MEDIO') { %>
                        <span style="background: #fef3c7; color: #d97706; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">⚠️ MÉDIO</span>
                      <% } else { %>
                        <span style="background: #dcfce7; color: #16a34a; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">✅ BAIXO</span>
                      <% } %>
                    </td>
                    <td>
                      <% 
                        const sugestao = Math.max(50, item.estoque_atual + Math.ceil(item.media_pedidos || 0) + 20);
                      %>
                      <strong><%= sugestao %> unidades</strong>
                    </td>
                    <td>
                      <button class="btn btn-primary" onclick="criarPedidoAutomatico('<%= item.produto_nome %>', <%= sugestao %>)">
                        📋 Criar Pedido
                      </button>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="5" style="text-align: center; color: #666; padding: 40px;">
                    <div>
                      <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                      <strong>Nenhuma sugestão disponível</strong><br>
                      <small>O sistema analisará as vendas para gerar sugestões automáticas</small>
                    </div>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Lista de Pedidos -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📋 Histórico de Pedidos</h3>
          <button class="btn btn-primary" onclick="novoPedido()">+ Novo Pedido</button>
        </div>
        <div class="section-content">
          <table class="data-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Fornecedor</th>
                <th>Quantidade</th>
                <th>Valor</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (typeof pedidos !== 'undefined' && pedidos.length > 0) { %>
                <% pedidos.forEach(pedido => { %>
                  <tr>
                    <td><%= new Date(pedido.data_pedido).toLocaleDateString('pt-BR') %></td>
                    <td><strong><%= pedido.produto_nome || 'N/A' %></strong></td>
                    <td><%= pedido.plataforma_nome || 'N/A' %></td>
                    <td><%= pedido.quantidade %></td>
                    <td><strong>R$ <%= parseFloat(pedido.valor_total || 0).toFixed(2) %></strong></td>
                    <td>
                      <% if (pedido.status === 'pendente') { %>
                        <span style="background: #fef3c7; color: #d97706; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">⏳ Pendente</span>
                      <% } else if (pedido.status === 'aprovado') { %>
                        <span style="background: #dbeafe; color: #2563eb; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">✅ Aprovado</span>
                      <% } else if (pedido.status === 'entregue') { %>
                        <span style="background: #dcfce7; color: #16a34a; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">📦 Entregue</span>
                      <% } else { %>
                        <span style="background: #fee2e2; color: #dc2626; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">❌ Cancelado</span>
                      <% } %>
                    </td>
                    <td>
                      <% if (pedido.status === 'pendente') { %>
                        <button class="btn btn-primary" onclick="aprovarPedido(<%= pedido.id_pedido %>)" style="padding: 6px 12px; font-size: 12px;">
                          ✅ Aprovar
                        </button>
                      <% } else { %>
                        <button class="btn btn-secondary" onclick="verDetalhes(<%= pedido.id_pedido %>)" style="padding: 6px 12px; font-size: 12px;">
                          👁️ Ver
                        </button>
                      <% } %>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="7" style="text-align: center; color: #666; padding: 40px;">
                    <div>
                      <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                      <strong>Nenhum pedido encontrado</strong><br>
                      <small>Crie seu primeiro pedido clicando no botão "Novo Pedido"</small>
                    </div>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </main>
  </div>

  <script>
    function gerarSugestoes() {
      const btn = event.target;
      btn.innerHTML = '🔄 Gerando...';
      btn.disabled = true;
      
      setTimeout(() => {
        btn.innerHTML = '🔄 Atualizar Sugestões';
        btn.disabled = false;
        alert('✅ Sugestões atualizadas com base nas vendas recentes!');
        window.location.reload();
      }, 2000);
    }

    function criarPedidoAutomatico(produto, quantidade) {
      if (confirm(`Criar pedido automático para:\n\nProduto: ${produto}\nQuantidade: ${quantidade} unidades\n\nConfirmar?`)) {
        alert(`✅ Pedido criado com sucesso!\n\nProduto: ${produto}\nQuantidade: ${quantidade} unidades\nStatus: Pendente`);
        window.location.reload();
      }
    }

    function novoPedido() {
      alert('🚧 Funcionalidade em desenvolvimento\n\nEm breve você poderá criar pedidos manuais através de um formulário completo.');
    }

    function aprovarPedido(id) {
      if (confirm(`Aprovar pedido #${id}?`)) {
        alert(`✅ Pedido #${id} aprovado com sucesso!`);
        window.location.reload();
      }
    }

    function verDetalhes(id) {
      alert(`📋 Detalhes do Pedido #${id}\n\nEsta funcionalidade abrirá uma modal com todos os detalhes do pedido.`);
    }

    console.log('📋 Página de pedidos carregada');
  </script>
</body>
</html>
