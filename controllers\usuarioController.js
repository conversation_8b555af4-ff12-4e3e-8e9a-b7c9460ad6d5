// controllers/usuarioController.js

const Usuario = require('../models/usuarioModel');

// Renderizar página de perfil
const renderPerfil = async (req, res) => {
  try {
    console.log('👤 Carregando página de perfil...');

    // Simular usuário logado (em produção viria da sessão)
    const userId = 1;
    let usuario = null;
    let metodosPagamento = [];

    try {
      // Buscar dados do usuário
      usuario = await Usuario.getById(userId);
      metodosPagamento = await Usuario.getMetodosPagamento(userId);

      console.log('✅ Dados do usuário carregados do banco');
    } catch (dbError) {
      console.log('⚠️ Banco não disponível, usando dados de demonstração:', dbError.message);

      // Dados de demonstração
      usuario = {
        id_usuario: 1,
        nome: 'Administrador PCR',
        email: '<EMAIL>',
        telefone: '+55 11 99999-9999',
        cargo: 'Administrador',
        empresa_nome: 'PCR Labor',
        empresa_cnpj: '12.345.678/0001-90',
        avatar: '/assets/avatar-default.png',
        created_at: new Date('2024-01-01')
      };

      metodosPagamento = [
        {
          id_metodo: 1,
          tipo: 'boleto',
          descricao: 'Boleto Bancário',
          dados_pagamento: JSON.stringify({
            banco: 'Banco do Brasil',
            agencia: '1234-5',
            conta: '67890-1'
          }),
          ativo: true
        },
        {
          id_metodo: 2,
          tipo: 'transferencia',
          descricao: 'Transferência Bancária',
          dados_pagamento: JSON.stringify({
            banco: 'Itaú',
            agencia: '0987',
            conta: '12345-6',
            pix: '<EMAIL>'
          }),
          ativo: true
        },
        {
          id_metodo: 3,
          tipo: 'link',
          descricao: 'Link de Pagamento',
          dados_pagamento: JSON.stringify({
            gateway: 'PagSeguro',
            link: 'https://pagseguro.uol.com.br/checkout/v2/payment.html?code=ABC123'
          }),
          ativo: false
        }
      ];
    }

    // Processar dados de pagamento
    const metodosPagamentoProcessados = metodosPagamento.map(metodo => {
      let dadosPagamento = {};
      try {
        dadosPagamento = typeof metodo.dados_pagamento === 'string'
          ? JSON.parse(metodo.dados_pagamento)
          : metodo.dados_pagamento;
      } catch (e) {
        dadosPagamento = {};
      }

      return {
        ...metodo,
        dados_pagamento: dadosPagamento
      };
    });

    // Estatísticas do usuário
    const stats = {
      tempoNaEmpresa: usuario.created_at ? Math.floor((new Date() - new Date(usuario.created_at)) / (1000 * 60 * 60 * 24)) : 0,
      metodosPagamentoAtivos: metodosPagamentoProcessados.filter(m => m.ativo).length,
      ultimoLogin: new Date().toLocaleDateString('pt-BR'),
      sessaoAtiva: true
    };

    res.render('pages/perfil-correct', {
      pageTitle: 'Perfil - PCR Labor',
      currentPage: 'perfil',
      usuario,
      metodosPagamento: metodosPagamentoProcessados,
      stats
    });
  } catch (error) {
    console.error('❌ Erro ao carregar perfil:', error);
    res.status(500).render('pages/error', {
      pageTitle: 'Erro - PCR Labor',
      error: 'Erro ao carregar perfil do usuário'
    });
  }
};

// API endpoints
const getAllUsuarios = async (req, res) => {
  try {
    const usuarios = await Usuario.getAll();
    res.status(200).json({
      success: true,
      data: usuarios,
      message: 'Usuários recuperados com sucesso'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const getUsuarioById = async (req, res) => {
  try {
    const usuario = await Usuario.getById(req.params.id);
    if (usuario) {
      res.status(200).json({
        success: true,
        data: usuario,
        message: 'Usuário encontrado'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const createUsuario = async (req, res) => {
  try {
    const { nome, email, senha, id_empresa, telefone, cargo, avatar } = req.body;
    const newUsuario = await Usuario.create({ nome, email, senha, id_empresa, telefone, cargo, avatar });
    res.status(201).json({
      success: true,
      data: newUsuario,
      message: 'Usuário criado com sucesso'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const updateUsuario = async (req, res) => {
  try {
    const { nome, email, telefone, cargo, avatar } = req.body;
    const updatedUsuario = await Usuario.update(req.params.id, { nome, email, telefone, cargo, avatar });
    if (updatedUsuario) {
      res.status(200).json({
        success: true,
        data: updatedUsuario,
        message: 'Usuário atualizado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const updatePassword = async (req, res) => {
  try {
    const { novaSenha } = req.body;
    const updated = await Usuario.updatePassword(req.params.id, novaSenha);
    if (updated) {
      res.status(200).json({
        success: true,
        message: 'Senha atualizada com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const deleteUsuario = async (req, res) => {
  try {
    const deleted = await Usuario.delete(req.params.id);
    if (deleted) {
      res.status(200).json({
        success: true,
        message: 'Usuário deletado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const getMetodosPagamento = async (req, res) => {
  try {
    const metodos = await Usuario.getMetodosPagamento(req.params.id);
    res.status(200).json({
      success: true,
      data: metodos,
      message: 'Métodos de pagamento recuperados com sucesso'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const addMetodoPagamento = async (req, res) => {
  try {
    const { tipo, descricao, dados_pagamento, ativo } = req.body;
    const newMetodo = await Usuario.addMetodoPagamento(req.params.id, { tipo, descricao, dados_pagamento, ativo });
    res.status(201).json({
      success: true,
      data: newMetodo,
      message: 'Método de pagamento adicionado com sucesso'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const updateMetodoPagamento = async (req, res) => {
  try {
    const { tipo, descricao, dados_pagamento, ativo } = req.body;
    const updatedMetodo = await Usuario.updateMetodoPagamento(req.params.metodoId, { tipo, descricao, dados_pagamento, ativo });
    if (updatedMetodo) {
      res.status(200).json({
        success: true,
        data: updatedMetodo,
        message: 'Método de pagamento atualizado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Método de pagamento não encontrado'
      });
    }
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
};

const deleteMetodoPagamento = async (req, res) => {
  try {
    const deleted = await Usuario.deleteMetodoPagamento(req.params.metodoId);
    if (deleted) {
      res.status(200).json({
        success: true,
        message: 'Método de pagamento deletado com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Método de pagamento não encontrado'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

module.exports = {
  renderPerfil,
  getAllUsuarios,
  getUsuarioById,
  createUsuario,
  updateUsuario,
  updatePassword,
  deleteUsuario,
  getMetodosPagamento,
  addMetodoPagamento,
  updateMetodoPagamento,
  deleteMetodoPagamento
};
