/* Reset básico para remover estilos padrão do navegador */
*
 {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  h1 {
  margin: 0;
  padding-top: 35px;
  padding-bottom: 5px;
}

  .bemVindo{
  margin: 0;
  padding-top: 20px;
  padding-bottom: 5px;
  text-align: center;
  }


header nav {
  padding-top: 35px;
  padding-bottom: 5px;
}

  /* Estilo para o corpo da página */
  body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #f0f0f0;
    color: #333;
    margin: 0;
    padding: 0;
  }

  .header-container {
  display: flex;
  align-items: center;
  gap: 40px;
  justify-content: flex-start; /* coloca tudo à esquerda */
  padding: 0 20px;
}
.logo-container {
  background-color: white; /* ou a cor que quiser */
  padding: 10px;
  width: 250px;
  height: -100px;
  display: flex;
  border-radius: 15px; /* opcional: cantos arredondados */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* sombra opcional */
}

.logo {
  max-width: 90%;
  max-height: 90%;
}

.h3 {
  margin: 0;
  padding-top: 10px;
  padding-bottom: 5px;
}
.graficos {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin: 0px 20px;
  padding: 10px 20px;
  margin-bottom: 2opx;

}

.grafico {
  max-width: 75vw;
  height: 35vh;
}

  /* Estilo para o cabeçalho */
  header {
    background-color: #018820;
    color: #fff;
    padding: 10px 0;
    text-align: center-20px;
  }

  /* Estilo para a navegação dentro do cabeçalho */
  header nav ul {
    list-style-type: none;
  }

  header nav ul li {
    display: inline;
    margin-right: 20px;
  }

  header nav ul li a {
    color: #fff;
    text-decoration: none;
  }

  header nav ul li a:hover {
    text-decoration: underline;
  }

  /* Estilo para o conteúdo principal */
  main {
    padding: 20px;
  }

  /* Estilo para o rodapé */
  footer {
    margin-top: 20px;
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 10px 0;
    bottom: 0;
    width: 100%;
  }

  /* Estilos para páginas de conteúdo */
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .container h2 {
    color: #018820;
    margin-bottom: 20px;
    border-bottom: 2px solid #018820;
    padding-bottom: 10px;
  }

  .container h3 {
    color: #333;
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .container p {
    margin-bottom: 15px;
    text-align: justify;
  }

  .container ul {
    margin-left: 20px;
    margin-bottom: 15px;
  }

  .container ul li {
    margin-bottom: 5px;
  }

  /* Estilos para formulário de contato */
  .contact-info {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 30px;
  }

  .contact-form {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
  }

  .form-group input,
  .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
  }

  .form-group input:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #018820;
    box-shadow: 0 0 5px rgba(1, 136, 32, 0.3);
  }

  button[type="submit"] {
    background-color: #018820;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  button[type="submit"]:hover {
    background-color: #016a1a;
  }

  /* Estilo para link ativo na navegação */
  header nav ul li a.active {
    font-weight: bold;
    text-decoration: underline;
  }
