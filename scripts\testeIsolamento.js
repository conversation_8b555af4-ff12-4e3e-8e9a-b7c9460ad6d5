#!/usr/bin/env node

/**
 * Script para testar o isolamento de dados entre empresas
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testeIsolamento() {
  try {
    console.log('🧪 TESTE FINAL: ISOLAMENTO DE DADOS ENTRE EMPRESAS');
    console.log('=' .repeat(60));

    // Usuários para teste
    const usuarios = [
      { nome: 'Lula', email: '<EMAIL>', senha: '123456', empresa: 'Lulass' },
      { nome: 'Teste Final', email: '<EMAIL>', senha: 'usuario123', empresa: 'PCR Labor' },
      { nome: 'Maria', email: '<EMAIL>', senha: 'maria123', empresa: 'Exemplo Comércio 6633 LTDA' }
    ];

    for (let i = 0; i < usuarios.length; i++) {
      const user = usuarios[i];
      
      console.log(`\n🧪 TESTE ${i + 1}: ${user.nome} (${user.empresa})`);
      console.log('-' .repeat(50));

      // Criar cliente com cookies
      const client = axios.create({
        baseURL: BASE_URL,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      try {
        // 1. Fazer login
        console.log(`🔐 Fazendo login: ${user.email}`);
        await client.post('/login', `email=${user.email}&senha=${user.senha}`, {
          maxRedirects: 0,
          validateStatus: (status) => status >= 200 && status < 400
        });
        console.log('✅ Login realizado');

        // 2. Testar API de produtos
        console.log('📦 Testando API de produtos...');
        const produtosResponse = await client.get('/api/produtos');
        const produtos = produtosResponse.data.data || [];
        
        console.log(`   📊 Produtos encontrados: ${produtos.length}`);
        if (produtos.length > 0) {
          console.log(`   📋 Primeiro produto: ${produtos[0].nome} (${produtos[0].empresa_nome})`);
          
          // Verificar se todos os produtos são da mesma empresa
          const empresasUnicas = [...new Set(produtos.map(p => p.empresa_nome))];
          if (empresasUnicas.length === 1) {
            console.log(`   ✅ Isolamento OK: Todos os produtos são da empresa "${empresasUnicas[0]}"`);
          } else {
            console.log(`   ❌ PROBLEMA: Produtos de múltiplas empresas: ${empresasUnicas.join(', ')}`);
          }
        } else {
          console.log('   ℹ️ Nenhum produto encontrado (empresa pode não ter produtos)');
        }

        // 3. Testar API de vendas
        console.log('💰 Testando API de vendas...');
        const vendasResponse = await client.get('/api/vendas');
        const vendas = vendasResponse.data.data || [];
        
        console.log(`   📊 Vendas encontradas: ${vendas.length}`);
        if (vendas.length > 0) {
          console.log(`   📋 Primeira venda: ${vendas[0].produto_nome} (${vendas[0].empresa_nome})`);
          
          // Verificar se todas as vendas são da mesma empresa
          const empresasUnicas = [...new Set(vendas.map(v => v.empresa_nome))];
          if (empresasUnicas.length === 1) {
            console.log(`   ✅ Isolamento OK: Todas as vendas são da empresa "${empresasUnicas[0]}"`);
          } else {
            console.log(`   ❌ PROBLEMA: Vendas de múltiplas empresas: ${empresasUnicas.join(', ')}`);
          }
        } else {
          console.log('   ℹ️ Nenhuma venda encontrada (empresa pode não ter vendas)');
        }

        // 4. Verificar informações da empresa na resposta
        if (produtosResponse.data.empresa_info) {
          const empresaInfo = produtosResponse.data.empresa_info;
          console.log(`🏢 Empresa na sessão: ${empresaInfo.nome_empresa} (ID: ${empresaInfo.id_empresa})`);
          console.log(`👤 Usuário logado: ${empresaInfo.usuario_logado}`);
        }

        // 5. Fazer logout
        console.log('🚪 Fazendo logout...');
        await client.get('/logout');
        console.log('✅ Logout realizado');

      } catch (error) {
        if (error.response && error.response.status === 302) {
          console.log('✅ Redirecionamento correto');
        } else {
          console.log(`❌ Erro: ${error.message}`);
        }
      }
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🎯 RESUMO DO TESTE:');
    console.log('✅ Se cada usuário viu apenas dados da sua empresa = SUCESSO');
    console.log('❌ Se algum usuário viu dados de outras empresas = PROBLEMA');
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('💥 Erro no teste:', error.message);
  }
}

// Executar teste
if (require.main === module) {
  testeIsolamento();
}

module.exports = { testeIsolamento };
