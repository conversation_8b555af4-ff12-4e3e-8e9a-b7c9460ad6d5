/* ===== CSS SEGUINDO WIREFRAME CORRETO ===== */

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Variáveis */
:root {
  --primary-green: #018820;
  --primary-dark: #016a1a;
  --navbar-bg: #ffffff;
  --content-bg: #f8f9fa;
  --text-dark: #333333;
  --text-gray: #666666;
  --border-gray: #e0e0e0;
  --card-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
  --navbar-height: 70px;
}

/* Layout principal */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--content-bg);
  color: var(--text-dark);
  line-height: 1.6;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== NAVBAR NO TOPO (SEGUINDO WIREFRAME) ===== */
.top-navbar {
  background-color: var(--navbar-bg);
  height: var(--navbar-height);
  border-bottom: 1px solid var(--border-gray);
  box-shadow: var(--card-shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
}

/* Logo no navbar */
.navbar-logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.navbar-logo img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: contain;
}

.navbar-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-green);
}

/* Navegação horizontal */
.navbar-nav {
  display: flex;
  align-items: center;
  gap: 30px;
  list-style: none;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  color: var(--text-dark);
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
  background-color: var(--primary-green);
  color: white;
}

.nav-icon {
  font-size: 16px;
}

/* Área do usuário */
.navbar-user {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid var(--border-gray);
  border-radius: 20px;
  padding: 8px 15px;
  width: 250px;
}

.search-input {
  border: none;
  background: none;
  outline: none;
  flex: 1;
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.user-avatar {
  width: 35px;
  height: 35px;
  background-color: var(--primary-green);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* ===== CONTEÚDO PRINCIPAL ===== */
.main-content {
  margin-top: var(--navbar-height);
  padding: 30px;
  min-height: calc(100vh - var(--navbar-height));
}

/* Header da página */
.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.page-subtitle {
  color: var(--text-gray);
  font-size: 16px;
}

/* ===== CARDS DE MÉTRICAS (OBJETIVOS) ===== */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background-color: white;
  border-radius: 12px;
  padding: 25px;
  border: 1px solid var(--border-gray);
  box-shadow: var(--card-shadow);
  transition: transform 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: linear-gradient(135deg, var(--primary-green), var(--primary-dark));
  color: white;
}

.metric-trend {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
}

.trend-up {
  background: #dcfce7;
  color: #16a34a;
}

.trend-down {
  background: #fee2e2;
  color: #dc2626;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.metric-label {
  color: var(--text-gray);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== SEÇÕES DE CONTEÚDO ===== */
.content-section {
  background-color: white;
  border-radius: 12px;
  border: 1px solid var(--border-gray);
  box-shadow: var(--card-shadow);
  margin-bottom: 30px;
  overflow: hidden;
}

.section-header {
  padding: 20px 25px;
  border-bottom: 1px solid var(--border-gray);
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-dark);
  margin: 0;
}

.section-content {
  padding: 25px;
}

/* ===== TABELAS ===== */
.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background-color: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: var(--text-gray);
  font-size: 12px;
  text-transform: uppercase;
  border-bottom: 1px solid var(--border-gray);
}

.data-table td {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  color: var(--text-dark);
  vertical-align: middle;
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

/* ===== BOTÕES ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-green), var(--primary-dark));
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(1, 136, 32, 0.3);
}

.btn-secondary {
  background-color: #f8f9fa;
  color: var(--text-dark);
  border: 1px solid var(--border-gray);
}

.btn-secondary:hover {
  background-color: #e9ecef;
}

/* ===== GRÁFICOS ===== */
.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  position: relative;
  height: 300px;
}

/* ===== RESPONSIVO ===== */
@media (max-width: 768px) {
  .navbar-nav {
    display: none;
  }
  
  .top-navbar {
    padding: 0 15px;
  }
  
  .main-content {
    padding: 20px 15px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .search-box {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .navbar-user {
    gap: 10px;
  }
  
  .search-box {
    width: 150px;
  }
  
  .metric-card {
    padding: 20px;
  }
  
  .metric-value {
    font-size: 24px;
  }
}
