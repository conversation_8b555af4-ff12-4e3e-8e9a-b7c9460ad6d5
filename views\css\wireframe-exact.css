/* ===== WIREFRAME EXACT DESIGN - PCR LABOR ===== */

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Variáveis baseadas no wireframe */
:root {
  /* Cores do wireframe */
  --primary-green: #018820;
  --primary-dark: #016a1a;
  --sidebar-bg: #2c3e50;
  --sidebar-text: #ecf0f1;
  --content-bg: #f8f9fa;
  --white: #ffffff;
  --text-dark: #333333;
  --text-gray: #666666;
  --border-gray: #e0e0e0;
  --card-shadow: 0 2px 4px rgba(0,0,0,0.1);

  /* Dimensões exatas do wireframe */
  --sidebar-width: 250px;
  --header-height: 60px;
  --card-height: 120px;
  --table-row-height: 50px;
}

/* Layout principal seguindo wireframe */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--content-bg);
  color: var(--text-dark);
  line-height: 1.6;
}

.app-layout {
  display: flex;
  min-height: 100vh;
}

/* ===== SIDEBAR EXATA DO WIREFRAME ===== */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Header da sidebar */
.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
  width: 60px;
  height: 60px;
  margin: 0 auto 10px;
  border-radius: 50%;
  object-fit: contain;
  background: white;
  padding: 5px;
}

.sidebar-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-green);
  margin: 0;
}

/* Navegação da sidebar */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin: 2px 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--sidebar-text);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-link:hover,
.nav-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-left-color: var(--primary-green);
}

.nav-icon {
  margin-right: 12px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* ===== MAIN CONTENT EXATO DO WIREFRAME ===== */
.main-content {
  margin-left: var(--sidebar-width);
  flex: 1;
  min-height: 100vh;
  background-color: var(--content-bg);
}

/* Header/Topbar exato do wireframe */
.topbar {
  background-color: var(--white);
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  border-bottom: 1px solid var(--border-gray);
  box-shadow: var(--card-shadow);
}

.topbar-left h1 {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-dark);
  margin: 0;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid var(--border-gray);
  border-radius: 4px;
  padding: 8px 12px;
  width: 250px;
}

.search-input {
  border: none;
  background: none;
  outline: none;
  flex: 1;
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background-color: var(--primary-green);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

/* ===== CONTENT AREA EXATA DO WIREFRAME ===== */
.content-area {
  padding: 30px;
}

/* Cards grid exato do wireframe */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background-color: var(--white);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--border-gray);
  box-shadow: var(--card-shadow);
  height: var(--card-height);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.card-icon {
  font-size: 20px;
  margin-right: 10px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.card-title {
  font-size: 14px;
  color: var(--text-gray);
  margin: 0;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--text-dark);
  margin: 5px 0;
}

.card-subtitle {
  font-size: 12px;
  color: var(--text-gray);
}

/* ===== TABELAS EXATAS DO WIREFRAME ===== */
.table-section {
  background-color: var(--white);
  border-radius: 8px;
  border: 1px solid var(--border-gray);
  box-shadow: var(--card-shadow);
  margin-bottom: 20px;
}

.table-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-gray);
  background-color: #f8f9fa;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--text-dark);
  margin: 0;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background-color: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: var(--text-gray);
  font-size: 12px;
  text-transform: uppercase;
  border-bottom: 1px solid var(--border-gray);
  height: var(--table-row-height);
}

.data-table td {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  color: var(--text-dark);
  height: var(--table-row-height);
  vertical-align: middle;
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

/* ===== BOTÕES EXATOS DO WIREFRAME ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-green);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: #f8f9fa;
  color: var(--text-dark);
  border: 1px solid var(--border-gray);
}

.btn-secondary:hover {
  background-color: #e9ecef;
}

/* ===== RESPONSIVO ===== */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .content-area {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }
}
