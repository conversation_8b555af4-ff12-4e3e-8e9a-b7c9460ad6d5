const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const produtoController = require('../controllers/produtoController');
const vendaController = require('../controllers/vendaController');

// Rota principal - redireciona para login
router.get('/', (req, res) => {
  res.redirect('/login');
});

// Rotas de autenticação
router.get('/login', dashboardController.renderLogin);
router.post('/login', dashboardController.processLogin);

// Dashboard principal
router.get('/dashboard', dashboardController.renderDashboard);

// APIs do dashboard
router.post('/api/sync-data', dashboardController.syncData);
router.get('/api/metrics', dashboardController.getMetrics);

// Rotas de páginas
router.get('/produtos', produtoController.renderProdutos);
router.get('/estoque', produtoController.renderEstoque);
router.get('/vendas', vendaController.renderVendas);
router.get('/plataformas', vendaController.renderPlataformas);

module.exports = router;
