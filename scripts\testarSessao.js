#!/usr/bin/env node

/**
 * Script para testar o sistema de sessão e isolamento de dados
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testarSessao() {
  try {
    console.log('🔐 Testando sistema de sessão e isolamento de dados...\n');

    // Criar uma instância do axios com cookies para manter sessão
    const client = axios.create({
      baseURL: BASE_URL,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    // Teste 1: Login com usuário Lula (Empresa: Lulass)
    console.log('🧪 TESTE 1: Login com Lula (Empresa: Lulass)');
    console.log('=' .repeat(50));
    
    try {
      const loginResponse = await client.post('/login', 'email=<EMAIL>&senha=lula123', {
        maxRedirects: 0,
        validateStatus: function (status) {
          return status >= 200 && status < 400;
        }
      });
      
      console.log('✅ Login realizado com sucesso');
      
      // Buscar dados do dashboard
      const dashboardResponse = await client.get('/dashboard');
      console.log('✅ Dashboard carregado');
      
      // Buscar produtos via API
      const produtosResponse = await client.get('/api/produtos');
      const produtos = produtosResponse.data.data || produtosResponse.data;
      
      console.log(`📦 Produtos encontrados: ${produtos.length}`);
      if (produtos.length > 0) {
        console.log(`📋 Primeiro produto: ${produtos[0].nome} (Empresa: ${produtos[0].empresa_nome})`);
        console.log(`🏢 ID da empresa: ${produtos[0].id_empresa}`);
      }
      
      // Buscar vendas via API
      const vendasResponse = await client.get('/api/vendas');
      const vendas = vendasResponse.data.data || vendasResponse.data;
      
      console.log(`💰 Vendas encontradas: ${vendas.length}`);
      if (vendas.length > 0) {
        console.log(`📋 Primeira venda: ${vendas[0].produto_nome} (Empresa: ${vendas[0].empresa_nome})`);
      }
      
    } catch (error) {
      if (error.response && error.response.status === 302) {
        console.log('✅ Login redirecionado corretamente');
      } else {
        console.log('❌ Erro no login:', error.message);
      }
    }

    console.log('\n' + '=' .repeat(50));
    
    // Fazer logout
    console.log('🚪 Fazendo logout...');
    await client.get('/logout');
    console.log('✅ Logout realizado');

    console.log('\n🧪 TESTE 2: Login com Teste Final (Empresa: PCR Labor)');
    console.log('=' .repeat(50));
    
    try {
      const loginResponse2 = await client.post('/login', 'email=<EMAIL>&senha=usuario123', {
        maxRedirects: 0,
        validateStatus: function (status) {
          return status >= 200 && status < 400;
        }
      });
      
      console.log('✅ Login realizado com sucesso');
      
      // Buscar produtos via API
      const produtosResponse2 = await client.get('/api/produtos');
      const produtos2 = produtosResponse2.data.data || produtosResponse2.data;
      
      console.log(`📦 Produtos encontrados: ${produtos2.length}`);
      if (produtos2.length > 0) {
        console.log(`📋 Primeiro produto: ${produtos2[0].nome} (Empresa: ${produtos2[0].empresa_nome})`);
        console.log(`🏢 ID da empresa: ${produtos2[0].id_empresa}`);
      }
      
      // Buscar vendas via API
      const vendasResponse2 = await client.get('/api/vendas');
      const vendas2 = vendasResponse2.data.data || vendasResponse2.data;
      
      console.log(`💰 Vendas encontradas: ${vendas2.length}`);
      if (vendas2.length > 0) {
        console.log(`📋 Primeira venda: ${vendas2[0].produto_nome} (Empresa: ${vendas2[0].empresa_nome})`);
      }
      
    } catch (error) {
      if (error.response && error.response.status === 302) {
        console.log('✅ Login redirecionado corretamente');
      } else {
        console.log('❌ Erro no login:', error.message);
      }
    }

    console.log('\n' + '=' .repeat(50));
    console.log('🎉 Teste de sessão concluído!');

  } catch (error) {
    console.error('💥 Erro no teste:', error.message);
  }
}

// Executar teste
if (require.main === module) {
  testarSessao();
}

module.exports = { testarSessao };
