/* ===== WIREFRAME DESIGN SYSTEM - PCR LABOR ===== */

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Variáveis baseadas nos wireframes */
:root {
  /* Cores principais dos wireframes */
  --primary-color: #018820;
  --primary-dark: #016a1a;
  --background-gray: #f5f5f5;
  --white: #ffffff;
  --text-dark: #333333;
  --text-gray: #666666;
  --border-gray: #e0e0e0;
  --sidebar-bg: #2c3e50;
  --sidebar-text: #ecf0f1;
  
  /* Dimensões dos wireframes */
  --sidebar-width: 250px;
  --header-height: 60px;
  --card-padding: 20px;
  --border-radius: 8px;
}

/* Layout principal seguindo wireframes */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-gray);
  color: var(--text-dark);
  line-height: 1.6;
}

.app-container {
  display: flex;
  min-height: 100vh;
}

/* ===== SIDEBAR (Wireframe Left Panel) ===== */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
}

.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;
  border-radius: 50%;
  object-fit: cover;
}

.sidebar-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
}

/* Navegação da sidebar */
.sidebar-nav {
  padding: 20px 0;
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin: 5px 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--sidebar-text);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-link:hover,
.nav-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-left-color: var(--primary-color);
}

.nav-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

/* Footer da sidebar */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 18px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: bold;
  font-size: 14px;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
}

.logout-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #e74c3c;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  font-size: 14px;
}

.logout-btn:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

/* ===== MAIN CONTENT (Wireframe Right Panel) ===== */
.main-content {
  margin-left: var(--sidebar-width);
  flex: 1;
  min-height: 100vh;
}

/* Header/Topbar */
.topbar {
  background-color: var(--white);
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  border-bottom: 1px solid var(--border-gray);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.topbar-left {
  display: flex;
  align-items: center;
}

.breadcrumb {
  font-size: 14px;
  color: var(--text-gray);
}

.breadcrumb .current {
  color: var(--primary-color);
  font-weight: bold;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid var(--border-gray);
  border-radius: 20px;
  padding: 8px 15px;
}

.search-input {
  border: none;
  background: none;
  outline: none;
  padding: 0;
  width: 200px;
  font-size: 14px;
}

.search-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-gray);
  margin-left: 10px;
}

/* ===== CONTENT AREA (Wireframe Main Panel) ===== */
.content-area {
  padding: 30px;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-gray);
}

/* ===== CARDS (Wireframe Card Components) ===== */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: var(--card-padding);
  border: 1px solid var(--border-gray);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.card-icon {
  font-size: 24px;
  margin-right: 12px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.card-title {
  font-size: 14px;
  color: var(--text-gray);
  margin: 0;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  color: var(--text-dark);
  margin: 10px 0;
}

.card-trend {
  font-size: 12px;
  color: var(--text-gray);
}

/* ===== TABLES (Wireframe Table Components) ===== */
.table-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-gray);
  overflow: hidden;
  margin-bottom: 30px;
}

.table-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-gray);
  background-color: #f8f9fa;
}

.table-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-dark);
  margin: 0;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background-color: #f8f9fa;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: var(--text-gray);
  font-size: 12px;
  text-transform: uppercase;
  border-bottom: 1px solid var(--border-gray);
}

.data-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  color: var(--text-dark);
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

/* ===== BUTTONS (Wireframe Button Components) ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: #f8f9fa;
  color: var(--text-dark);
  border: 1px solid var(--border-gray);
}

.btn-secondary:hover {
  background-color: #e9ecef;
}

/* ===== RESPONSIVE (Wireframe Mobile) ===== */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .cards-grid {
    grid-template-columns: 1fr;
  }
  
  .content-area {
    padding: 20px;
  }
}
