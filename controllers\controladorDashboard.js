// controllers/dashboardController.js

const Produto = require('../models/modeloProdutos');
const Venda = require('../models/modeloVendas');
const Plataforma = require('../models/modeloPlataformas');
const ServicoIntegracaoDados = require('../services/servicoIntegracaoDados');

const renderDashboard = async (req, res) => {
  try {
    console.log('🔄 Carregando dashboard com dados em tempo real...');

    // Verificar se usuário está logado
    if (!req.session.usuario) {
      console.log('❌ Usuário não logado, redirecionando para login');
      return res.redirect('/login');
    }

    const usuario = req.session.usuario;
    console.log(`👤 Dashboard para: ${usuario.nome} (${usuario.empresa_nome})`);
    console.log(`🏢 Empresa ID: ${usuario.id_empresa}`);

    // Sincronizar dados das plataformas automaticamente
    await ServicoIntegracaoDados.syncPlatformData();

    // Calcular métricas em tempo real para a empresa do usuário
    const metrics = await ServicoIntegracaoDados.calculateMetrics(usuario.id_empresa);

    // Buscar dados atualizados filtrados por empresa
    const [produtos, vendas, sugestoes] = await Promise.all([
      Produto.getAll(usuario.id_empresa),
      Venda.getAll(usuario.id_empresa),
      ServicoIntegracaoDados.autoRestockSuggestions(usuario.id_empresa)
    ]);

    console.log('✅ Dashboard carregado com métricas em tempo real');

    res.render('pages/dashboard', {
      pageTitle: 'Dashboard - PCR Labor',
      currentPage: 'dashboard',
      stats: {
        totalProdutos: metrics.totalProdutos,
        produtosEstoqueBaixo: metrics.produtosEstoqueBaixo,
        totalVendas: metrics.totalVendas,
        vendasHoje: metrics.vendasHoje,
        valorTotalVendas: metrics.valorTotalVendas,
        ticketMedio: metrics.ticketMedio,
        produtoMaisVendido: metrics.produtoMaisVendido
      },
      produtos: produtos.slice(0, 5),
      vendas: vendas.slice(0, 10),
      plataformas: metrics.plataformas,
      sugestoes: sugestoes.slice(0, 5),
      lastUpdate: new Date().toLocaleString('pt-BR')
    });
  } catch (error) {
    console.error('❌ Erro ao carregar dashboard:', error);

    // Fallback para dados básicos se houver erro
    try {
      const usuario = req.session.usuario;
      const id_empresa = usuario ? usuario.id_empresa : 1; // Fallback para empresa 1

      const [produtos, vendas, plataformas] = await Promise.all([
        Produto.getAll(id_empresa),
        Venda.getAll(id_empresa),
        Plataforma.getVendasPorPlataforma()
      ]);

      const stats = {
        totalProdutos: produtos.length,
        produtosEstoqueBaixo: produtos.filter(p => p.estoque_atual <= 10).length,
        totalVendas: vendas.length,
        vendasHoje: 0,
        valorTotalVendas: vendas.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0),
        ticketMedio: 0,
        produtoMaisVendido: 'N/A'
      };

      res.render('pages/dashboard-correct', {
        pageTitle: 'Dashboard - PCR Labor',
        currentPage: 'dashboard',
        stats,
        produtos: produtos.slice(0, 5),
        vendas: vendas.slice(0, 10),
        plataformas: plataformas.map(p => ({
          ...p,
          valor_total: parseFloat(p.valor_total || 0)
        })),
        sugestoes: [],
        lastUpdate: new Date().toLocaleString('pt-BR'),
        warning: 'Dados em modo básico - algumas funcionalidades podem estar limitadas'
      });
    } catch (fallbackError) {
      res.status(500).render('pages/error', {
        pageTitle: 'Erro - PCR Labor',
        error: 'Erro ao carregar dashboard: ' + error.message
      });
    }
  }
};

const renderLogin = (req, res) => {
  try {
    res.render('pages/login', {
      pageTitle: 'Login - PCR Labor',
      currentPage: 'login'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao carregar página de login'
    });
  }
};

const processLogin = async (req, res) => {
  try {
    const { email, senha } = req.body;

    console.log('Tentativa de login:', { email, senha }); // Debug

    // Validação simples para demonstração
    if (email === '<EMAIL>' && senha === 'admin123') {
      console.log('Login bem-sucedido, redirecionando para dashboard'); // Debug
      return res.redirect('/dashboard');
    } else {
      console.log('Credenciais inválidas'); // Debug
      return res.render('pages/login', {
        pageTitle: 'Login - PCR Labor',
        currentPage: 'login',
        error: 'Email ou senha inválidos'
      });
    }
  } catch (error) {
    console.error('Erro no processLogin:', error); // Debug
    return res.status(500).render('pages/login', {
      pageTitle: 'Login - PCR Labor',
      currentPage: 'login',
      error: 'Erro interno do servidor'
    });
  }
};

const syncData = async (req, res) => {
  try {
    console.log('🔄 Sincronização manual iniciada...');

    const result = await ServicoIntegracaoDados.syncPlatformData();

    if (result.success) {
      res.json({
        success: true,
        message: 'Dados sincronizados com sucesso',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

const getMetrics = async (req, res) => {
  try {
    const metrics = await ServicoIntegracaoDados.calculateMetrics();
    res.json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

module.exports = {
  renderDashboard,
  renderLogin,
  processLogin,
  syncData,
  getMetrics
};
