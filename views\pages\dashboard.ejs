<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/estilos.css?v=<%= Date.now() %>">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- APENAS Header horizontal (Topo) - SEM SIDEBAR LATERAL -->
  <%- include('../components/cabecalho') %>

  <!-- Layout SEM SIDEBAR - Largura completa -->
  <div style="margin-top: 70px; padding: 30px; width: 100%; box-sizing: border-box;">

    <h1 class="page-title">Dashboard - Visão Geral do Sistema</h1>

    <!-- BLOCO 1: RESUMO DE VENDAS -->
    <div class="content-section" style="margin-bottom: 30px;">
      <div class="section-header">
        <h3 class="section-title">💰 Vendas - Resumo Executivo</h3>
        <a href="/vendas" class="btn btn-primary">Ver Detalhes</a>
      </div>
      <div class="section-content">
        <!-- Cards de métricas de vendas -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
          <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #018820, #02a025); color: white; border-radius: 12px;">
            <div style="font-size: 12px; opacity: 0.9; text-transform: uppercase; font-weight: 600;">Total de Vendas</div>
            <div style="font-size: 32px; font-weight: bold; margin: 10px 0;">R$ <%= stats.valorTotalVendas.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ".") %></div>
            <div style="font-size: 14px; opacity: 0.9;">Faturamento total</div>
          </div>

          <div style="text-align: center; padding: 20px; background: #f8f9fa; border: 2px solid #e0e0e0; border-radius: 12px;">
            <div style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Vendas Realizadas</div>
            <div style="font-size: 32px; font-weight: bold; color: #333; margin: 10px 0;"><%= stats.totalVendas %></div>
            <div style="font-size: 14px; color: #666;">Transações</div>
          </div>

          <div style="text-align: center; padding: 20px; background: #f8f9fa; border: 2px solid #e0e0e0; border-radius: 12px;">
            <div style="font-size: 12px; color: #666; text-transform: uppercase; font-weight: 600;">Vendas Hoje</div>
            <div style="font-size: 32px; font-weight: bold; color: #333; margin: 10px 0;"><%= stats.vendasHoje %></div>
            <div style="font-size: 14px; color: #666;">Hoje</div>
          </div>

          <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; border-radius: 12px;">
            <div style="font-size: 12px; opacity: 0.9; text-transform: uppercase; font-weight: 600;">Crescimento</div>
            <div style="font-size: 32px; font-weight: bold; margin: 10px 0;">+12%</div>
            <div style="font-size: 14px; opacity: 0.9;">vs mês anterior</div>
          </div>
        </div>

        <!-- Gráfico de vendas -->
        <div style="height: 250px; background: white; border-radius: 8px; padding: 20px;">
          <canvas id="vendasChart"></canvas>
        </div>
      </div>
    </div>

    <!-- BLOCO 2: RESUMO DE ESTOQUE -->
    <div class="content-section" style="margin-bottom: 30px;">
      <div class="section-header">
        <h3 class="section-title">📦 Estoque - Status Atual</h3>
        <a href="/estoque" class="btn btn-primary">Ver Detalhes</a>
      </div>
      <div class="section-content">
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
          <!-- Total em estoque -->
          <div style="text-align: center; padding: 25px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #16a34a;">
            <div style="font-size: 36px; font-weight: bold; color: #16a34a; margin-bottom: 10px;"><%= stats.totalProdutos %></div>
            <div style="font-size: 14px; color: #666; margin-bottom: 15px;">Produtos Cadastrados</div>
            <div style="height: 120px;">
              <canvas id="estoqueChart"></canvas>
            </div>
          </div>

          <!-- Estoque baixo -->
          <div style="text-align: center; padding: 25px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #f59e0b;">
            <div style="font-size: 36px; font-weight: bold; color: #f59e0b; margin-bottom: 10px;"><%= stats.produtosEstoqueBaixo %></div>
            <div style="font-size: 14px; color: #666; margin-bottom: 15px;">Estoque Baixo</div>
            <div style="height: 120px;">
              <canvas id="estoqueBaixoChart"></canvas>
            </div>
          </div>

          <!-- Produtos mais vendidos -->
          <div style="text-align: center; padding: 25px; background: #f8f9fa; border-radius: 12px; border-left: 4px solid #3b82f6;">
            <div style="font-size: 20px; font-weight: bold; color: #3b82f6; margin-bottom: 10px;"><%= stats.produtoMaisVendido %></div>
            <div style="font-size: 14px; color: #666; margin-bottom: 15px;">Mais Vendido</div>
            <div style="height: 120px;">
              <canvas id="maisVendidoChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- BLOCO 3: RESUMO DE PEDIDOS -->
    <div class="content-section" style="margin-bottom: 30px;">
      <div class="section-header">
        <h3 class="section-title">📋 Pedidos - Status Atual</h3>
        <a href="/pedidos" class="btn btn-primary">Ver Detalhes</a>
      </div>
      <div class="section-content">
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
          <div style="text-align: center; padding: 20px; background: #fff8e1; border-radius: 8px; border-left: 4px solid #f59e0b;">
            <div style="font-size: 28px; font-weight: bold; color: #f59e0b;">12</div>
            <div style="color: #666; font-size: 14px;">Pendentes</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f0fff4; border-radius: 8px; border-left: 4px solid #16a34a;">
            <div style="font-size: 28px; font-weight: bold; color: #16a34a;">8</div>
            <div style="color: #666; font-size: 14px;">Aprovados</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #3b82f6;">
            <div style="font-size: 28px; font-weight: bold; color: #3b82f6;">R$ 15.400</div>
            <div style="color: #666; font-size: 14px;">Valor Total</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #faf5ff; border-radius: 8px; border-left: 4px solid #8b5cf6;">
            <div style="font-size: 28px; font-weight: bold; color: #8b5cf6;">5-7 dias</div>
            <div style="color: #666; font-size: 14px;">Prazo Médio</div>
          </div>
        </div>

        <!-- Gráfico de pedidos -->
        <div style="height: 200px; background: white; border-radius: 8px; padding: 20px;">
          <canvas id="pedidosChart"></canvas>
        </div>
      </div>
    </div>

    <!-- BLOCO 4: RESUMO DE PLATAFORMAS -->
    <div class="content-section" style="margin-bottom: 30px;">
      <div class="section-header">
        <h3 class="section-title">🌐 Plataformas - Performance</h3>
        <a href="/plataformas" class="btn btn-primary">Ver Detalhes</a>
      </div>
      <div class="section-content">
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 20px;">
          <!-- Shopee -->
          <div style="text-align: center; padding: 25px; background: #fff5f0; border-radius: 12px; border-left: 4px solid #ff6600;">
            <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px;">🛍️ Shopee</div>
            <div style="font-size: 28px; font-weight: bold; color: #ff6600; margin-bottom: 5px;">R$ 18.500</div>
            <div style="color: #16a34a; font-size: 14px; font-weight: bold;">+15% vs mês anterior</div>
          </div>

          <!-- Mercado Livre -->
          <div style="text-align: center; padding: 25px; background: #f0f8ff; border-radius: 12px; border-left: 4px solid #3483fa;">
            <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px;">🛒 Mercado Livre</div>
            <div style="font-size: 28px; font-weight: bold; color: #3483fa; margin-bottom: 5px;">R$ 22.300</div>
            <div style="color: #16a34a; font-size: 14px; font-weight: bold;">+8% vs mês anterior</div>
          </div>

          <!-- PCR Labor -->
          <div style="text-align: center; padding: 25px; background: #f0fff4; border-radius: 12px; border-left: 4px solid #018820;">
            <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px;">🌐 PCR Labor</div>
            <div style="font-size: 28px; font-weight: bold; color: #018820; margin-bottom: 5px;">R$ 12.800</div>
            <div style="color: #16a34a; font-size: 14px; font-weight: bold;">+25% vs mês anterior</div>
          </div>
        </div>

        <!-- Gráfico comparativo de plataformas -->
        <div style="height: 250px; background: white; border-radius: 8px; padding: 20px;">
          <canvas id="plataformasChart"></canvas>
        </div>
      </div>
    </div>

  </div>

  <script>
    // GRÁFICO 1: Vendas (linha)
    const vendasCtx = document.getElementById('vendasChart').getContext('2d');
    new Chart(vendasCtx, {
      type: 'line',
      data: {
        labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
        datasets: [{
          label: 'Vendas (R$)',
          data: [1200, 1900, 800, 1500, 2200, 1800, 2500],
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#018820',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false }
        },
        scales: {
          y: { beginAtZero: true, grid: { color: '#f0f0f0' } },
          x: { grid: { display: false } }
        }
      }
    });

    // GRÁFICO 2: Estoque (doughnut)
    const estoqueCtx = document.getElementById('estoqueChart').getContext('2d');
    new Chart(estoqueCtx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [70, 20, 10],
          backgroundColor: ['#16a34a', '#f59e0b', '#ef4444'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: false } }
      }
    });

    // GRÁFICO 3: Estoque Baixo (doughnut)
    const estoqueBaixoCtx = document.getElementById('estoqueBaixoChart').getContext('2d');
    new Chart(estoqueBaixoCtx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [<%= stats.produtosEstoqueBaixo %>, <%= stats.totalProdutos - stats.produtosEstoqueBaixo %>],
          backgroundColor: ['#f59e0b', '#e5e7eb'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: false } }
      }
    });

    // GRÁFICO 4: Mais Vendido (bar)
    const maisVendidoCtx = document.getElementById('maisVendidoChart').getContext('2d');
    new Chart(maisVendidoCtx, {
      type: 'bar',
      data: {
        labels: ['COVID', 'Influenza', 'Hepatite'],
        datasets: [{
          data: [45, 32, 23],
          backgroundColor: '#3b82f6',
          borderRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: false } },
        scales: {
          y: { beginAtZero: true, display: false },
          x: { display: false }
        }
      }
    });

    // GRÁFICO 5: Pedidos (bar)
    const pedidosCtx = document.getElementById('pedidosChart').getContext('2d');
    new Chart(pedidosCtx, {
      type: 'bar',
      data: {
        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai'],
        datasets: [{
          label: 'Pedidos',
          data: [8, 12, 6, 15, 18],
          backgroundColor: '#3b82f6',
          borderRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: false } },
        scales: {
          y: { beginAtZero: true, grid: { color: '#f0f0f0' } },
          x: { grid: { display: false } }
        }
      }
    });

    // GRÁFICO 6: Plataformas (bar comparativo)
    const plataformasCtx = document.getElementById('plataformasChart').getContext('2d');
    new Chart(plataformasCtx, {
      type: 'bar',
      data: {
        labels: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio'],
        datasets: [
          {
            label: 'Shopee',
            data: [15000, 16500, 17200, 18000, 18500],
            backgroundColor: '#ff6600',
            borderRadius: 4
          },
          {
            label: 'Mercado Livre',
            data: [18000, 19500, 20800, 21500, 22300],
            backgroundColor: '#3483fa',
            borderRadius: 4
          },
          {
            label: 'PCR Labor',
            data: [8000, 9200, 10500, 11800, 12800],
            backgroundColor: '#018820',
            borderRadius: 4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: { usePointStyle: true, padding: 20 }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: { color: '#f0f0f0' },
            ticks: {
              callback: function(value) {
                return 'R$ ' + value.toLocaleString();
              }
            }
          },
          x: { grid: { display: false } }
        }
      }
    });

    console.log('📊 Dashboard SEM SIDEBAR carregado com todos os gráficos');
  </script>
</body>
</html>
