<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/dashboard.css">
</head>
<body>
  <%- include('../components/sidebar') %>
  
  <div class="main-content">
    <%- include('../components/topbar') %>
    
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>Dashboard</h1>
        <p>Visão geral do sistema PCR Labor</p>
      </div>
      
      <!-- Cards de estatísticas -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📦</div>
          <div class="stat-info">
            <h3><%= stats.totalProdutos %></h3>
            <p>Total de Produtos</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⚠️</div>
          <div class="stat-info">
            <h3><%= stats.produtosEstoqueBaixo %></h3>
            <p>Estoque Baixo</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-info">
            <h3><%= stats.totalVendas %></h3>
            <p>Total de Vendas</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📈</div>
          <div class="stat-info">
            <h3><%= stats.vendasHoje %></h3>
            <p>Vendas Hoje</p>
          </div>
        </div>
      </div>
      
      <!-- Gráficos e informações -->
      <div class="dashboard-grid">
        <div class="dashboard-card">
          <h3>Vendas por Plataforma</h3>
          <div class="chart-container">
            <% plataformas.forEach(plataforma => { %>
              <div class="chart-item">
                <span class="platform-name"><%= plataforma.nome %></span>
                <div class="chart-bar">
                  <div class="bar-fill" style="width: <%= (plataforma.valor_total / Math.max(...plataformas.map(p => p.valor_total || 0))) * 100 %>%"></div>
                </div>
                <span class="platform-value">R$ <%= (plataforma.valor_total || 0).toFixed(2) %></span>
              </div>
            <% }) %>
          </div>
        </div>
        
        <div class="dashboard-card">
          <h3>Produtos Recentes</h3>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>Produto</th>
                  <th>SKU</th>
                  <th>Estoque</th>
                  <th>Preço</th>
                </tr>
              </thead>
              <tbody>
                <% produtos.forEach(produto => { %>
                  <tr>
                    <td><%= produto.nome %></td>
                    <td><%= produto.sku %></td>
                    <td class="<%= produto.estoque_atual <= 10 ? 'low-stock' : '' %>">
                      <%= produto.estoque_atual %>
                    </td>
                    <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                  </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        </div>
        
        <div class="dashboard-card">
          <h3>Vendas Recentes</h3>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>Data</th>
                  <th>Produto</th>
                  <th>Quantidade</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
                <% vendas.forEach(venda => { %>
                  <tr>
                    <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    <td><%= venda.produto_nome %></td>
                    <td><%= venda.quantidade %></td>
                    <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                  </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        </div>
        
        <div class="dashboard-card">
          <h3>Resumo Financeiro</h3>
          <div class="financial-summary">
            <div class="financial-item">
              <span class="label">Valor Total de Vendas:</span>
              <span class="value">R$ <%= stats.valorTotalVendas.toFixed(2) %></span>
            </div>
            <div class="financial-item">
              <span class="label">Vendas do Mês:</span>
              <span class="value">
                <% 
                  const vendasMesAtual = vendas.filter(v => {
                    const dataVenda = new Date(v.data);
                    const agora = new Date();
                    return dataVenda.getMonth() === agora.getMonth() && dataVenda.getFullYear() === agora.getFullYear();
                  });
                  const valorMesAtual = vendasMesAtual.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0);
                %>
                R$ <%= valorMesAtual.toFixed(2) %>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
