<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe.css">
</head>
<body>
  <div class="app-container">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content -->
    <div class="main-content">
      <!-- Topbar -->
      <%- include('../components/topbar') %>
      
      <!-- Content Area -->
      <div class="content-area">
        <!-- Page Header -->
        <div class="page-header">
          <h1 class="page-title">Dashboard</h1>
          <p class="page-subtitle">Visão geral do sistema PCR Labor</p>
        </div>
        
        <!-- Cards de Estatísticas -->
        <div class="cards-grid">
          <!-- Card 1: Total de Produtos -->
          <div class="card">
            <div class="card-header">
              <div class="card-icon">📦</div>
              <div>
                <h3 class="card-title">Total de Produtos</h3>
              </div>
            </div>
            <div class="card-value"><%= stats.totalProdutos %></div>
            <div class="card-trend">+2 produtos este mês</div>
          </div>
          
          <!-- Card 2: Estoque Baixo -->
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #fff3cd;">⚠️</div>
              <div>
                <h3 class="card-title">Estoque Baixo</h3>
              </div>
            </div>
            <div class="card-value" style="color: #856404;"><%= stats.produtosEstoqueBaixo %></div>
            <div class="card-trend">Produtos com menos de 10 unidades</div>
          </div>
          
          <!-- Card 3: Vendas Hoje -->
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #d1ecf1;">📈</div>
              <div>
                <h3 class="card-title">Vendas Hoje</h3>
              </div>
            </div>
            <div class="card-value" style="color: #0c5460;"><%= stats.vendasHoje %></div>
            <div class="card-trend">Meta: 10 vendas/dia</div>
          </div>
          
          <!-- Card 4: Faturamento Total -->
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #d4edda;">💰</div>
              <div>
                <h3 class="card-title">Faturamento Total</h3>
              </div>
            </div>
            <div class="card-value" style="color: #155724;">R$ <%= stats.valorTotalVendas.toFixed(2) %></div>
            <div class="card-trend">+15% vs mês anterior</div>
          </div>
        </div>
        
        <!-- Gráfico de Vendas por Plataforma -->
        <div class="table-container">
          <div class="table-header">
            <h3 class="table-title">📊 Vendas por Plataforma</h3>
          </div>
          <div style="padding: 20px;">
            <% if (plataformas && plataformas.length > 0) { %>
              <% plataformas.forEach(plataforma => { %>
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                  <div style="min-width: 120px; font-weight: 500;"><%= plataforma.nome %></div>
                  <div style="flex: 1; height: 20px; background-color: #f0f0f0; border-radius: 10px; margin: 0 15px; overflow: hidden;">
                    <div style="height: 100%; background: linear-gradient(90deg, #018820, #02a025); width: <%= Math.min((parseFloat(plataforma.valor_total || 0) / Math.max(...plataformas.map(p => parseFloat(p.valor_total || 0)), 1)) * 100, 100) %>%; border-radius: 10px;"></div>
                  </div>
                  <div style="min-width: 100px; text-align: right; font-weight: 600;">R$ <%= parseFloat(plataforma.valor_total || 0).toFixed(2) %></div>
                </div>
              <% }) %>
            <% } else { %>
              <p style="text-align: center; color: #666; padding: 40px;">Nenhuma venda encontrada</p>
            <% } %>
          </div>
        </div>
        
        <!-- Tabelas Side by Side -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
          <!-- Produtos Recentes -->
          <div class="table-container">
            <div class="table-header">
              <h3 class="table-title">🧪 Produtos Recentes</h3>
            </div>
            <table class="data-table">
              <thead>
                <tr>
                  <th>Produto</th>
                  <th>SKU</th>
                  <th>Preço</th>
                  <th>Estoque</th>
                </tr>
              </thead>
              <tbody>
                <% if (produtos && produtos.length > 0) { %>
                  <% produtos.forEach(produto => { %>
                    <tr>
                      <td><%= produto.nome %></td>
                      <td><%= produto.sku %></td>
                      <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                      <td>
                        <span style="color: <%= produto.estoque_atual <= 10 ? '#856404' : '#155724' %>;">
                          <%= produto.estoque_atual %>
                        </span>
                      </td>
                    </tr>
                  <% }) %>
                <% } else { %>
                  <tr>
                    <td colspan="4" style="text-align: center; color: #666; padding: 20px;">
                      Nenhum produto encontrado
                    </td>
                  </tr>
                <% } %>
              </tbody>
            </table>
          </div>
          
          <!-- Vendas Recentes -->
          <div class="table-container">
            <div class="table-header">
              <h3 class="table-title">💰 Vendas Recentes</h3>
            </div>
            <table class="data-table">
              <thead>
                <tr>
                  <th>Produto</th>
                  <th>Qtd</th>
                  <th>Valor</th>
                  <th>Data</th>
                </tr>
              </thead>
              <tbody>
                <% if (vendas && vendas.length > 0) { %>
                  <% vendas.forEach(venda => { %>
                    <tr>
                      <td><%= venda.produto_nome || 'N/A' %></td>
                      <td><%= venda.quantidade %></td>
                      <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                      <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    </tr>
                  <% }) %>
                <% } else { %>
                  <tr>
                    <td colspan="4" style="text-align: center; color: #666; padding: 20px;">
                      Nenhuma venda encontrada
                    </td>
                  </tr>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Botões de Ação -->
        <div style="margin-top: 30px; display: flex; gap: 15px;">
          <button class="btn btn-primary" onclick="syncData()">
            🔄 Sincronizar Dados
          </button>
          <button class="btn btn-secondary" onclick="exportReport()">
            📊 Exportar Relatório
          </button>
          <button class="btn btn-secondary" onclick="refreshDashboard()">
            ↻ Atualizar Dashboard
          </button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Funcionalidades do dashboard
    async function syncData() {
      const button = event.target;
      const originalText = button.innerHTML;
      
      try {
        button.innerHTML = '🔄 Sincronizando...';
        button.disabled = true;
        
        const response = await fetch('/api/sync-data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        const result = await response.json();
        
        if (result.success) {
          showNotification('✅ Dados sincronizados com sucesso!', 'success');
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          showNotification('❌ Erro na sincronização: ' + result.error, 'error');
        }
      } catch (error) {
        showNotification('❌ Erro de conexão: ' + error.message, 'error');
      } finally {
        button.innerHTML = originalText;
        button.disabled = false;
      }
    }
    
    function exportReport() {
      showNotification('📊 Gerando relatório...', 'info');
      // Implementar exportação
    }
    
    function refreshDashboard() {
      window.location.reload();
    }
    
    function showNotification(message, type = 'info') {
      // Criar notificação toast
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        animation: slideIn 0.3s ease;
      `;
      
      const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
      };
      
      notification.style.backgroundColor = colors[type] || colors.info;
      notification.textContent = message;
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    }
    
    // Auto-refresh a cada 5 minutos
    setInterval(() => {
      console.log('Auto-refresh dashboard');
      // Implementar refresh automático
    }, 5 * 60 * 1000);
  </script>
</body>
</html>
