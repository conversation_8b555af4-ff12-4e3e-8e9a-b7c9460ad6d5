<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    .chart-container {
      position: relative;
      height: 300px;
      margin: 20px 0;
    }

    .dashboard-charts {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 20px;
      margin-bottom: 30px;
    }

    .chart-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .chart-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <div class="app-layout">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Topbar -->
      <%- include('../components/topbar') %>

      <!-- Content Area -->
      <div class="content-area">
        <!-- Cards de Métricas (4 cards em linha) -->
        <div class="cards-grid">
          <!-- Card 1: Total de Produtos -->
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📦</div>
              <h3 class="card-title">Total de Produtos</h3>
            </div>
            <div class="card-value"><%= stats.totalProdutos %></div>
            <div class="card-subtitle">produtos cadastrados</div>
          </div>

          <!-- Card 2: Estoque Baixo -->
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">⚠️</div>
              <h3 class="card-title">Estoque Baixo</h3>
            </div>
            <div class="card-value"><%= stats.produtosEstoqueBaixo %></div>
            <div class="card-subtitle">produtos com estoque baixo</div>
          </div>

          <!-- Card 3: Vendas Hoje -->
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📈</div>
              <h3 class="card-title">Vendas Hoje</h3>
            </div>
            <div class="card-value"><%= stats.vendasHoje %></div>
            <div class="card-subtitle">vendas realizadas hoje</div>
          </div>

          <!-- Card 4: Faturamento -->
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">💰</div>
              <h3 class="card-title">Faturamento</h3>
            </div>
            <div class="card-value">R$ <%= stats.valorTotalVendas.toFixed(2) %></div>
            <div class="card-subtitle">faturamento total</div>
          </div>
        </div>

        <!-- Gráficos do Dashboard -->
        <div class="dashboard-charts">
          <!-- Gráfico de Vendas por Mês -->
          <div class="chart-section">
            <h3 class="chart-title">Vendas por Mês</h3>
            <div class="chart-container">
              <canvas id="vendasMesChart"></canvas>
            </div>
          </div>

          <!-- Gráfico de Estoque -->
          <div class="chart-section">
            <h3 class="chart-title">Status do Estoque</h3>
            <div class="chart-container">
              <canvas id="estoqueChart"></canvas>
            </div>
          </div>
        </div>

        <!-- Tabela de Produtos Recentes -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Produtos Recentes</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Nome</th>
                <th>SKU</th>
                <th>Preço</th>
                <th>Estoque</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% if (produtos && produtos.length > 0) { %>
                <% produtos.slice(0, 5).forEach(produto => { %>
                  <tr>
                    <td><%= produto.nome %></td>
                    <td><%= produto.sku %></td>
                    <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                    <td><%= produto.estoque_atual %></td>
                    <td>
                      <% if (produto.estoque_atual <= 10) { %>
                        <span style="color: #dc3545;">Baixo</span>
                      <% } else { %>
                        <span style="color: #28a745;">Normal</span>
                      <% } %>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="5" style="text-align: center; color: #666;">
                    Nenhum produto encontrado
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>

        <!-- Tabela de Vendas Recentes -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Vendas Recentes</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Quantidade</th>
                <th>Valor</th>
                <th>Plataforma</th>
              </tr>
            </thead>
            <tbody>
              <% if (vendas && vendas.length > 0) { %>
                <% vendas.slice(0, 5).forEach(venda => { %>
                  <tr>
                    <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    <td><%= venda.produto_nome || 'N/A' %></td>
                    <td><%= venda.quantidade %></td>
                    <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                    <td><%= venda.plataforma_nome || 'N/A' %></td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="5" style="text-align: center; color: #666;">
                    Nenhuma venda encontrada
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Dados para os gráficos
    const produtosData = <%- JSON.stringify(produtos) %>;
    const vendasData = <%- JSON.stringify(vendas) %>;

    // Gráfico de Vendas por Mês (simulado)
    const vendasMesCtx = document.getElementById('vendasMesChart').getContext('2d');
    const mesesLabels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'];
    const vendasPorMes = [45, 52, 38, 67, 73, 89]; // Dados simulados

    new Chart(vendasMesCtx, {
      type: 'line',
      data: {
        labels: mesesLabels,
        datasets: [{
          label: 'Vendas',
          data: vendasPorMes,
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });

    // Gráfico de Status do Estoque
    const estoqueCtx = document.getElementById('estoqueChart').getContext('2d');
    const produtosNormal = produtosData.filter(p => p.estoque_atual > 10).length;
    const produtosBaixo = produtosData.filter(p => p.estoque_atual <= 10 && p.estoque_atual > 0).length;
    const produtosZero = produtosData.filter(p => p.estoque_atual === 0).length;

    new Chart(estoqueCtx, {
      type: 'doughnut',
      data: {
        labels: ['Normal', 'Baixo', 'Zero'],
        datasets: [{
          data: [produtosNormal, produtosBaixo, produtosZero],
          backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });
  </script>
</body>
</html>
