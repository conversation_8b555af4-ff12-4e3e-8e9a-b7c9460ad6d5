<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
</head>
<body>
  <div class="app-layout">
    <!-- Sidebar -->
    <%- include('../components/sidebar') %>
    
    <!-- Main Content -->
    <div class="main-content">
      <!-- Topbar -->
      <%- include('../components/topbar') %>
      
      <!-- Content Area -->
      <div class="content-area">
        <!-- Cards de Métricas (4 cards em linha) -->
        <div class="cards-grid">
          <!-- Card 1: Total de Produtos -->
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📦</div>
              <h3 class="card-title">Total de Produtos</h3>
            </div>
            <div class="card-value"><%= stats.totalProdutos %></div>
            <div class="card-subtitle">produtos cadastrados</div>
          </div>
          
          <!-- Card 2: Estoque Baixo -->
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">⚠️</div>
              <h3 class="card-title">Estoque Baixo</h3>
            </div>
            <div class="card-value"><%= stats.produtosEstoqueBaixo %></div>
            <div class="card-subtitle">produtos com estoque baixo</div>
          </div>
          
          <!-- Card 3: Vendas Hoje -->
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">📈</div>
              <h3 class="card-title">Vendas Hoje</h3>
            </div>
            <div class="card-value"><%= stats.vendasHoje %></div>
            <div class="card-subtitle">vendas realizadas hoje</div>
          </div>
          
          <!-- Card 4: Faturamento -->
          <div class="metric-card">
            <div class="card-header">
              <div class="card-icon">💰</div>
              <h3 class="card-title">Faturamento</h3>
            </div>
            <div class="card-value">R$ <%= stats.valorTotalVendas.toFixed(2) %></div>
            <div class="card-subtitle">faturamento total</div>
          </div>
        </div>
        
        <!-- Tabela de Produtos Recentes -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Produtos Recentes</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Nome</th>
                <th>SKU</th>
                <th>Preço</th>
                <th>Estoque</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% if (produtos && produtos.length > 0) { %>
                <% produtos.slice(0, 5).forEach(produto => { %>
                  <tr>
                    <td><%= produto.nome %></td>
                    <td><%= produto.sku %></td>
                    <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                    <td><%= produto.estoque_atual %></td>
                    <td>
                      <% if (produto.estoque_atual <= 10) { %>
                        <span style="color: #dc3545;">Baixo</span>
                      <% } else { %>
                        <span style="color: #28a745;">Normal</span>
                      <% } %>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="5" style="text-align: center; color: #666;">
                    Nenhum produto encontrado
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
        
        <!-- Tabela de Vendas Recentes -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Vendas Recentes</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Data</th>
                <th>Produto</th>
                <th>Quantidade</th>
                <th>Valor</th>
                <th>Plataforma</th>
              </tr>
            </thead>
            <tbody>
              <% if (vendas && vendas.length > 0) { %>
                <% vendas.slice(0, 5).forEach(venda => { %>
                  <tr>
                    <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    <td><%= venda.produto_nome || 'N/A' %></td>
                    <td><%= venda.quantidade %></td>
                    <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                    <td><%= venda.plataforma_nome || 'N/A' %></td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="5" style="text-align: center; color: #666;">
                    Nenhuma venda encontrada
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
