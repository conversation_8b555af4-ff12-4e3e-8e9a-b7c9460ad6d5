<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/dashboard.css">
</head>
<body>
  <div class="app-container">
    <%- include('../components/sidebar') %>

    <div class="main-content">
      <%- include('../components/topbar') %>

      <div class="dashboard-container">
        <div class="dashboard-header">
          <div class="header-content">
            <div class="header-text">
              <h1>Dashboard</h1>
              <p>Visão geral em tempo real do sistema PCR Labor</p>
              <% if (typeof lastUpdate !== 'undefined') { %>
                <span class="last-update">Última atualização: <%= lastUpdate %></span>
              <% } %>
              <% if (typeof warning !== 'undefined') { %>
                <div class="warning-badge">⚠️ <%= warning %></div>
              <% } %>
            </div>
            <div class="header-actions">
              <button class="btn btn-secondary" onclick="refreshData()">
                🔄 Atualizar Dados
              </button>
              <button class="btn btn-primary" onclick="syncPlatforms()">
                🔗 Sincronizar Plataformas
              </button>
            </div>
          </div>
        </div>

        <!-- Cards de estatísticas -->
        <div class="stats-grid">
          <div class="stat-card primary">
            <div class="stat-content">
              <div class="stat-icon">📦</div>
              <div class="stat-details">
                <h3><%= stats.totalProdutos %></h3>
                <p>Total de Produtos</p>
                <span class="stat-trend">+2 este mês</span>
              </div>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-content">
              <div class="stat-icon">⚠️</div>
              <div class="stat-details">
                <h3><%= stats.produtosEstoqueBaixo %></h3>
                <p>Estoque Baixo</p>
                <span class="stat-trend">Requer atenção</span>
              </div>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-content">
              <div class="stat-icon">💰</div>
              <div class="stat-details">
                <h3>R$ <%= stats.valorTotalVendas.toFixed(2) %></h3>
                <p>Faturamento Total</p>
                <span class="stat-trend">+15% vs mês anterior</span>
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-content">
              <div class="stat-icon">📈</div>
              <div class="stat-details">
                <h3><%= stats.vendasHoje %></h3>
                <p>Vendas Hoje</p>
                <span class="stat-trend">Meta: 10 vendas</span>
              </div>
            </div>
          </div>

          <% if (typeof stats.ticketMedio !== 'undefined') { %>
          <div class="stat-card secondary">
            <div class="stat-content">
              <div class="stat-icon">🎯</div>
              <div class="stat-details">
                <h3>R$ <%= stats.ticketMedio.toFixed(2) %></h3>
                <p>Ticket Médio</p>
                <span class="stat-trend">Por venda</span>
              </div>
            </div>
          </div>
          <% } %>

          <% if (typeof stats.produtoMaisVendido !== 'undefined') { %>
          <div class="stat-card accent">
            <div class="stat-content">
              <div class="stat-icon">🏆</div>
              <div class="stat-details">
                <h3><%= stats.produtoMaisVendido %></h3>
                <p>Produto Mais Vendido</p>
                <span class="stat-trend">Este mês</span>
              </div>
            </div>
          </div>
          <% } %>
        </div>

      <!-- Gráficos e informações -->
      <div class="dashboard-grid">
        <div class="dashboard-card">
          <h3>Vendas por Plataforma</h3>
          <div class="chart-container">
            <% if (plataformas && plataformas.length > 0) { %>
              <%
                const valores = plataformas.map(p => {
                  const valor = p.valor_total;
                  return typeof valor === 'number' ? valor : parseFloat(valor || 0);
                });
                const maxValor = Math.max(...valores, 1);
              %>
              <% plataformas.forEach(plataforma => { %>
                <%
                  const valorPlataforma = typeof plataforma.valor_total === 'number'
                    ? plataforma.valor_total
                    : parseFloat(plataforma.valor_total || 0);
                %>
                <div class="chart-item">
                  <span class="platform-name"><%= plataforma.nome %></span>
                  <div class="chart-bar">
                    <div class="bar-fill" style="width: <%= (valorPlataforma / maxValor) * 100 %>%"></div>
                  </div>
                  <span class="platform-value">R$ <%= valorPlataforma.toFixed(2) %></span>
                </div>
              <% }) %>
            <% } else { %>
              <p>Nenhuma plataforma encontrada.</p>
            <% } %>
          </div>
        </div>

        <div class="dashboard-card">
          <h3>Produtos Recentes</h3>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>Produto</th>
                  <th>SKU</th>
                  <th>Estoque</th>
                  <th>Preço</th>
                </tr>
              </thead>
              <tbody>
                <% produtos.forEach(produto => { %>
                  <tr>
                    <td><%= produto.nome %></td>
                    <td><%= produto.sku %></td>
                    <td class="<%= produto.estoque_atual <= 10 ? 'low-stock' : '' %>">
                      <%= produto.estoque_atual %>
                    </td>
                    <td>R$ <%= parseFloat(produto.preco).toFixed(2) %></td>
                  </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        </div>

        <div class="dashboard-card">
          <h3>Vendas Recentes</h3>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>Data</th>
                  <th>Produto</th>
                  <th>Quantidade</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
                <% vendas.forEach(venda => { %>
                  <tr>
                    <td><%= new Date(venda.data).toLocaleDateString('pt-BR') %></td>
                    <td><%= venda.produto_nome %></td>
                    <td><%= venda.quantidade %></td>
                    <td>R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></td>
                  </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        </div>

        <div class="dashboard-card">
          <h3>Resumo Financeiro</h3>
          <div class="financial-summary">
            <div class="financial-item">
              <span class="label">Valor Total de Vendas:</span>
              <span class="value">R$ <%= stats.valorTotalVendas.toFixed(2) %></span>
            </div>
            <div class="financial-item">
              <span class="label">Vendas do Mês:</span>
              <span class="value">
                <%
                  const vendasMesAtual = vendas.filter(v => {
                    const dataVenda = new Date(v.data);
                    const agora = new Date();
                    return dataVenda.getMonth() === agora.getMonth() && dataVenda.getFullYear() === agora.getFullYear();
                  });
                  const valorMesAtual = vendasMesAtual.reduce((total, v) => total + parseFloat(v.valor_total || 0), 0);
                %>
                R$ <%= valorMesAtual.toFixed(2) %>
              </span>
            </div>
          </div>
        </div>

        <!-- Sugestões de reposição (se disponível) -->
        <% if (typeof sugestoes !== 'undefined' && sugestoes.length > 0) { %>
        <div class="dashboard-card">
          <h3>🔄 Sugestões de Reposição Automática</h3>
          <div class="suggestions-list">
            <% sugestoes.forEach(sugestao => { %>
              <div class="suggestion-item">
                <div class="suggestion-info">
                  <strong><%= sugestao.produto %></strong>
                  <span class="sku">SKU: <%= sugestao.sku %></span>
                  <p><%= sugestao.motivo %></p>
                </div>
                <div class="suggestion-action">
                  <span class="current-stock">Atual: <%= sugestao.estoque_atual %></span>
                  <span class="suggested-stock">Sugerido: <%= sugestao.quantidade_sugerida %></span>
                  <button class="btn btn-sm btn-primary" onclick="applyRestock('<%= sugestao.sku %>', <%= sugestao.quantidade_sugerida %>)">
                    Aplicar
                  </button>
                </div>
              </div>
            <% }) %>
          </div>
        </div>
        <% } %>
      </div>
    </div>
    </div>
  </div>

  <script>
    // Funcionalidades de sincronização e atualização
    async function syncPlatforms() {
      const button = event.target;
      const originalText = button.innerHTML;

      try {
        button.innerHTML = '🔄 Sincronizando...';
        button.disabled = true;

        const response = await fetch('/api/sync-data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        const result = await response.json();

        if (result.success) {
          showNotification('✅ Dados sincronizados com sucesso!', 'success');
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          showNotification('❌ Erro na sincronização: ' + result.error, 'error');
        }
      } catch (error) {
        showNotification('❌ Erro de conexão: ' + error.message, 'error');
      } finally {
        button.innerHTML = originalText;
        button.disabled = false;
      }
    }

    async function refreshData() {
      try {
        const response = await fetch('/api/metrics');
        const result = await response.json();

        if (result.success) {
          showNotification('✅ Dados atualizados!', 'success');
          // Atualizar elementos da página com novos dados
          updateDashboardMetrics(result.data);
        } else {
          showNotification('❌ Erro ao atualizar dados', 'error');
        }
      } catch (error) {
        showNotification('❌ Erro de conexão', 'error');
      }
    }

    function updateDashboardMetrics(data) {
      // Atualizar cards de estatísticas
      document.querySelector('.stat-card.primary h3').textContent = data.totalProdutos;
      document.querySelector('.stat-card.warning h3').textContent = data.produtosEstoqueBaixo;
      document.querySelector('.stat-card.success h3').textContent = 'R$ ' + data.valorTotalVendas.toFixed(2);
      document.querySelector('.stat-card.info h3').textContent = data.vendasHoje;
    }

    function applyRestock(sku, quantity) {
      if (confirm(`Aplicar reposição de ${quantity} unidades para o produto ${sku}?`)) {
        showNotification('🔄 Aplicando reposição...', 'info');
        // Implementar lógica de reposição
        setTimeout(() => {
          showNotification('✅ Reposição aplicada com sucesso!', 'success');
        }, 1000);
      }
    }

    function showNotification(message, type = 'info') {
      // Criar elemento de notificação
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.textContent = message;

      // Estilos da notificação
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideIn 0.3s ease;
        max-width: 400px;
      `;

      // Cores por tipo
      const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
      };

      notification.style.backgroundColor = colors[type] || colors.info;

      // Adicionar ao DOM
      document.body.appendChild(notification);

      // Remover após 3 segundos
      setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    }

    // Auto-refresh a cada 5 minutos
    setInterval(() => {
      refreshData();
    }, 5 * 60 * 1000);

    // Adicionar animações CSS
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }

      @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
      }
    `;
    document.head.appendChild(style);
  </script>
</body>
</html>
