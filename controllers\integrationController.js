// controllers/integrationController.js

const PlatformIntegrationService = require('../services/platformIntegrationService');

// Sincronizar todas as plataformas
const syncAllPlatforms = async (req, res) => {
  try {
    console.log('🔄 Iniciando sincronização manual de todas as plataformas...');
    
    const results = await PlatformIntegrationService.syncAllPlatforms();
    
    res.status(200).json({
      success: true,
      data: results,
      message: 'Sincronização completa realizada com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro na sincronização:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erro durante a sincronização das plataformas'
    });
  }
};

// Buscar dados específicos do Mercado Livre
const fetchMercadoLivreData = async (req, res) => {
  try {
    console.log('🛒 Buscando dados específicos do Mercado Livre...');
    
    const data = await PlatformIntegrationService.fetchMercadoLivreData();
    
    res.status(200).json({
      success: true,
      data: data,
      message: 'Dados do Mercado Livre obtidos com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao buscar dados do Mercado Livre:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erro ao conectar com a API do Mercado Livre'
    });
  }
};

// Buscar dados específicos da Shopee
const fetchShopeeData = async (req, res) => {
  try {
    console.log('🛍️ Buscando dados específicos da Shopee...');
    
    const data = await PlatformIntegrationService.fetchShopeeData();
    
    res.status(200).json({
      success: true,
      data: data,
      message: 'Dados da Shopee obtidos com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao buscar dados da Shopee:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erro ao conectar com a API da Shopee'
    });
  }
};

// Obter métricas consolidadas
const getConsolidatedMetrics = async (req, res) => {
  try {
    console.log('📊 Gerando métricas consolidadas...');
    
    const metrics = await PlatformIntegrationService.getConsolidatedMetrics();
    
    res.status(200).json({
      success: true,
      data: metrics,
      message: 'Métricas consolidadas geradas com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao gerar métricas:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erro ao calcular métricas consolidadas'
    });
  }
};

// Processar venda manual
const processManualSale = async (req, res) => {
  try {
    const { platform, saleData } = req.body;
    
    console.log(`📝 Processando venda manual da ${platform}...`);
    
    // Simular dados de plataforma
    const platformData = {
      platform: platform,
      sales: [saleData]
    };
    
    await PlatformIntegrationService.processPlatformSales(platformData);
    
    res.status(200).json({
      success: true,
      message: 'Venda processada manualmente com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao processar venda manual:', error);
    res.status(400).json({
      success: false,
      error: error.message,
      message: 'Erro ao processar venda manual'
    });
  }
};

// Gerar relatório de integração
const getIntegrationReport = async (req, res) => {
  try {
    console.log('📋 Gerando relatório de integração...');
    
    const { startDate, endDate } = req.query;
    
    // Buscar métricas consolidadas
    const metrics = await PlatformIntegrationService.getConsolidatedMetrics();
    
    // Gerar relatório
    const report = {
      period: {
        start: startDate || 'N/A',
        end: endDate || 'N/A'
      },
      summary: {
        total_platforms: metrics.platforms.length,
        total_products: metrics.total_products,
        total_sales: metrics.total_sales,
        total_revenue: metrics.total_revenue,
        low_stock_alerts: metrics.low_stock_products
      },
      platforms: metrics.platforms,
      top_products: metrics.top_products,
      sales_trend: metrics.sales_trend,
      generated_at: new Date().toISOString()
    };
    
    res.status(200).json({
      success: true,
      data: report,
      message: 'Relatório de integração gerado com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao gerar relatório:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erro ao gerar relatório de integração'
    });
  }
};

// Testar conectividade das APIs
const testApiConnectivity = async (req, res) => {
  try {
    console.log('🔍 Testando conectividade das APIs...');
    
    const results = {
      mercado_livre: { status: 'unknown', response_time: 0, error: null },
      shopee: { status: 'unknown', response_time: 0, error: null },
      timestamp: new Date().toISOString()
    };
    
    // Testar Mercado Livre
    try {
      const startTime = Date.now();
      await PlatformIntegrationService.fetchMercadoLivreData();
      results.mercado_livre.response_time = Date.now() - startTime;
      results.mercado_livre.status = 'online';
    } catch (error) {
      results.mercado_livre.status = 'offline';
      results.mercado_livre.error = error.message;
    }
    
    // Testar Shopee
    try {
      const startTime = Date.now();
      await PlatformIntegrationService.fetchShopeeData();
      results.shopee.response_time = Date.now() - startTime;
      results.shopee.status = 'online';
    } catch (error) {
      results.shopee.status = 'offline';
      results.shopee.error = error.message;
    }
    
    res.status(200).json({
      success: true,
      data: results,
      message: 'Teste de conectividade concluído'
    });
  } catch (error) {
    console.error('❌ Erro no teste de conectividade:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erro durante o teste de conectividade'
    });
  }
};

// Configurar webhook para receber dados em tempo real
const configureWebhook = async (req, res) => {
  try {
    const { platform, webhook_url, events } = req.body;
    
    console.log(`🔗 Configurando webhook para ${platform}...`);
    
    // Simular configuração de webhook
    const webhookConfig = {
      platform: platform,
      webhook_url: webhook_url,
      events: events || ['order_created', 'order_updated', 'payment_confirmed'],
      status: 'active',
      created_at: new Date().toISOString()
    };
    
    res.status(200).json({
      success: true,
      data: webhookConfig,
      message: `Webhook configurado para ${platform} com sucesso`
    });
  } catch (error) {
    console.error('❌ Erro ao configurar webhook:', error);
    res.status(400).json({
      success: false,
      error: error.message,
      message: 'Erro ao configurar webhook'
    });
  }
};

// Receber dados via webhook
const receiveWebhookData = async (req, res) => {
  try {
    const { platform, event_type, data } = req.body;
    
    console.log(`📨 Recebendo webhook de ${platform}: ${event_type}`);
    
    // Processar dados do webhook baseado no tipo de evento
    switch (event_type) {
      case 'order_created':
        await PlatformIntegrationService.processSingleSale(data, 1); // ID da plataforma
        break;
      case 'order_updated':
        console.log('Pedido atualizado:', data);
        break;
      case 'payment_confirmed':
        console.log('Pagamento confirmado:', data);
        break;
      default:
        console.log('Evento não reconhecido:', event_type);
    }
    
    res.status(200).json({
      success: true,
      message: 'Webhook processado com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao processar webhook:', error);
    res.status(400).json({
      success: false,
      error: error.message,
      message: 'Erro ao processar dados do webhook'
    });
  }
};

module.exports = {
  syncAllPlatforms,
  fetchMercadoLivreData,
  fetchShopeeData,
  getConsolidatedMetrics,
  processManualSale,
  getIntegrationReport,
  testApiConnectivity,
  configureWebhook,
  receiveWebhookData
};
