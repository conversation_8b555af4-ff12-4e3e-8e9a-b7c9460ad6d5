<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-correct.css">
</head>
<body>
  <div class="app-container">
    <%- include('../components/navbar') %>
    
    <main class="main-content">
      <div class="page-header">
        <h1 class="page-title">Perfil</h1>
        <p class="page-subtitle">Gerencie suas informações pessoais e métodos de pagamento</p>
      </div>
      
      <!-- Informações do Usuário -->
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
        <!-- <PERSON><PERSON> Pessoais -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">👤 Informações Pessoais</h3>
            <button class="btn btn-primary" onclick="editarPerfil()">✏️ Editar</button>
          </div>
          <div class="section-content">
            <div style="text-align: center; margin-bottom: 30px;">
              <div style="width: 100px; height: 100px; border-radius: 50%; background: linear-gradient(135deg, #018820, #02a025); display: flex; align-items: center; justify-content: center; color: white; font-size: 36px; font-weight: bold; margin: 0 auto 15px;">
                <%= typeof usuario !== 'undefined' ? usuario.nome.charAt(0).toUpperCase() : 'A' %>
              </div>
              <h3 style="margin: 0; color: #333;"><%= typeof usuario !== 'undefined' ? usuario.nome : 'Administrador PCR' %></h3>
              <p style="margin: 5px 0 0; color: #666;"><%= typeof usuario !== 'undefined' ? usuario.cargo : 'Administrador' %></p>
            </div>
            
            <div style="display: grid; gap: 20px;">
              <div>
                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">Email</label>
                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px; color: #666;">
                  <%= typeof usuario !== 'undefined' ? usuario.email : '<EMAIL>' %>
                </div>
              </div>
              
              <div>
                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">Telefone</label>
                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px; color: #666;">
                  <%= typeof usuario !== 'undefined' ? usuario.telefone : '+55 11 99999-9999' %>
                </div>
              </div>
              
              <div>
                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">Empresa</label>
                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px; color: #666;">
                  <%= typeof usuario !== 'undefined' ? usuario.empresa_nome : 'PCR Labor' %>
                </div>
              </div>
              
              <div>
                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">CNPJ</label>
                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px; color: #666;">
                  <%= typeof usuario !== 'undefined' ? usuario.empresa_cnpj : '12.345.678/0001-90' %>
                </div>
              </div>
            </div>
            
            <div style="margin-top: 30px; display: flex; gap: 15px;">
              <button class="btn btn-primary" onclick="alterarSenha()">🔒 Alterar Senha</button>
              <button class="btn btn-secondary" onclick="baixarDados()">📄 Baixar Dados</button>
            </div>
          </div>
        </div>
        
        <!-- Estatísticas -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">📊 Estatísticas</h3>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 20px;">
              <div style="background: linear-gradient(135deg, #018820, #02a025); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                <div style="font-size: 32px; font-weight: bold; margin-bottom: 5px;">
                  <%= typeof stats !== 'undefined' ? stats.tempoNaEmpresa : 365 %>
                </div>
                <div style="font-size: 14px; opacity: 0.9;">Dias na empresa</div>
              </div>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px; color: #333;">
                  <%= typeof stats !== 'undefined' ? stats.metodosPagamentoAtivos : 2 %>
                </div>
                <div style="font-size: 14px; color: #666;">Métodos de pagamento ativos</div>
              </div>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; text-align: center;">
                <div style="font-size: 16px; font-weight: bold; margin-bottom: 5px; color: #333;">
                  <%= typeof stats !== 'undefined' ? stats.ultimoLogin : new Date().toLocaleDateString('pt-BR') %>
                </div>
                <div style="font-size: 14px; color: #666;">Último login</div>
              </div>
              
              <div style="background: #dcfce7; padding: 20px; border-radius: 12px; text-align: center;">
                <div style="font-size: 16px; font-weight: bold; margin-bottom: 5px; color: #16a34a;">
                  ✅ Online
                </div>
                <div style="font-size: 14px; color: #16a34a;">Status da sessão</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Métodos de Pagamento -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">💳 Métodos de Pagamento</h3>
          <button class="btn btn-primary" onclick="adicionarMetodo()">+ Adicionar Método</button>
        </div>
        <div class="section-content">
          <% if (typeof metodosPagamento !== 'undefined' && metodosPagamento.length > 0) { %>
            <div style="display: grid; gap: 20px;">
              <% metodosPagamento.forEach(metodo => { %>
                <div style="border: 1px solid #e0e0e0; border-radius: 12px; padding: 20px; background: #f8f9fa;">
                  <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 12px;">
                      <div style="width: 40px; height: 40px; border-radius: 8px; background: #018820; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                        <% if (metodo.tipo === 'boleto') { %>📄<% } else if (metodo.tipo === 'transferencia') { %>🏦<% } else { %>🔗<% } %>
                      </div>
                      <div>
                        <h4 style="margin: 0; color: #333;"><%= metodo.descricao %></h4>
                        <p style="margin: 0; color: #666; font-size: 14px;">
                          <% if (metodo.tipo === 'boleto') { %>Boleto Bancário<% } else if (metodo.tipo === 'transferencia') { %>Transferência/PIX<% } else { %>Link de Pagamento<% } %>
                        </p>
                      </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <span style="padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 600; <%= metodo.ativo ? 'background: #dcfce7; color: #16a34a;' : 'background: #fee2e2; color: #dc2626;' %>">
                        <%= metodo.ativo ? '✅ Ativo' : '❌ Inativo' %>
                      </span>
                      <button class="btn btn-secondary" onclick="editarMetodo(<%= metodo.id_metodo %>)" style="padding: 6px 12px; font-size: 12px;">✏️</button>
                    </div>
                  </div>
                  
                  <div style="color: #666; font-size: 14px; line-height: 1.5;">
                    <% if (metodo.tipo === 'boleto' && metodo.dados_pagamento && metodo.dados_pagamento.banco) { %>
                      <strong>Banco:</strong> <%= metodo.dados_pagamento.banco %><br>
                      <strong>Agência:</strong> <%= metodo.dados_pagamento.agencia %><br>
                      <strong>Conta:</strong> <%= metodo.dados_pagamento.conta %>
                    <% } else if (metodo.tipo === 'transferencia' && metodo.dados_pagamento && metodo.dados_pagamento.banco) { %>
                      <strong>Banco:</strong> <%= metodo.dados_pagamento.banco %><br>
                      <strong>Agência:</strong> <%= metodo.dados_pagamento.agencia %><br>
                      <strong>Conta:</strong> <%= metodo.dados_pagamento.conta %>
                      <% if (metodo.dados_pagamento.pix) { %><br><strong>PIX:</strong> <%= metodo.dados_pagamento.pix %><% } %>
                    <% } else if (metodo.tipo === 'link' && metodo.dados_pagamento && metodo.dados_pagamento.gateway) { %>
                      <strong>Gateway:</strong> <%= metodo.dados_pagamento.gateway %><br>
                      <strong>Status:</strong> Configurado
                    <% } %>
                  </div>
                </div>
              <% }) %>
            </div>
          <% } else { %>
            <div style="text-align: center; color: #666; padding: 60px 20px;">
              <div style="font-size: 64px; margin-bottom: 20px;">💳</div>
              <h3 style="margin: 0 0 10px; color: #333;">Nenhum método de pagamento</h3>
              <p style="margin: 0 0 30px;">Adicione métodos de pagamento para facilitar suas transações</p>
              <button class="btn btn-primary" onclick="adicionarMetodo()">+ Adicionar Primeiro Método</button>
            </div>
          <% } %>
        </div>
      </div>
    </main>
  </div>

  <script>
    function editarPerfil() {
      alert('🚧 Funcionalidade em desenvolvimento\n\nEm breve você poderá editar suas informações pessoais através de um formulário.');
    }

    function alterarSenha() {
      const novaSenha = prompt('Digite a nova senha:');
      if (novaSenha && novaSenha.length >= 6) {
        alert('✅ Senha alterada com sucesso!');
      } else if (novaSenha) {
        alert('❌ A senha deve ter pelo menos 6 caracteres.');
      }
    }

    function baixarDados() {
      alert('📄 Preparando download dos seus dados...\n\nEm breve você receberá um arquivo com todas as suas informações.');
    }

    function adicionarMetodo() {
      const tipo = prompt('Escolha o tipo de método:\n\n1 - Boleto Bancário\n2 - Transferência/PIX\n3 - Link de Pagamento\n\nDigite o número:');
      
      if (tipo === '1') {
        alert('✅ Método de boleto adicionado!\n\nConfiguração de boleto bancário criada com sucesso.');
      } else if (tipo === '2') {
        alert('✅ Método de transferência adicionado!\n\nConfiguração de transferência/PIX criada com sucesso.');
      } else if (tipo === '3') {
        alert('✅ Link de pagamento adicionado!\n\nConfiguração de link de pagamento criada com sucesso.');
      }
      
      if (tipo >= '1' && tipo <= '3') {
        setTimeout(() => window.location.reload(), 1000);
      }
    }

    function editarMetodo(id) {
      alert(`✏️ Editando método de pagamento #${id}\n\nEsta funcionalidade abrirá um formulário para editar os dados do método.`);
    }

    console.log('👤 Página de perfil carregada');
  </script>
</body>
</html>
