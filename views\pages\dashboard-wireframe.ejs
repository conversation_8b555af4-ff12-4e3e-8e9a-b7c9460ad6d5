<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-final.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- Header horizontal (Topo) com logo e menu -->
  <%- include('../components/header-horizontal') %>

  <!-- Layout principal -->
  <div class="main-layout">
    <!-- Área central para o conteúdo principal -->
    <main class="content-area">
      <!-- Dashboard Padronizado -->
      <h1 class="page-title">Dashboard</h1>

      <!-- Cards de métricas principais (seguindo padrão das outras páginas) -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
        <!-- Card Vendas -->
        <div class="summary-card">
          <div class="card-icon">💰</div>
          <div class="card-title">Total de Vendas</div>
          <div class="card-value">R$ <%= stats.valorTotalVendas.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ".") %></div>
        </div>

        <!-- Card Vendas Hoje -->
        <div class="summary-card">
          <div class="card-icon" style="background: #3b82f6;">📊</div>
          <div class="card-title">Vendas Hoje</div>
          <div class="card-value"><%= vendas.filter(v => new Date(v.data).toDateString() === new Date().toDateString()).length %></div>
        </div>

        <!-- Card Estoque -->
        <div class="summary-card">
          <div class="card-icon" style="background: #f59e0b;">📦</div>
          <div class="card-title">Produtos em Estoque</div>
          <div class="card-value"><%= produtos.reduce((total, p) => total + p.estoque_atual, 0) %></div>
        </div>

        <!-- Card Crescimento -->
        <div class="summary-card">
          <div class="card-icon" style="background: #16a34a;">📈</div>
          <div class="card-title">Crescimento</div>
          <div class="card-value">+12%</div>
        </div>
      </div>

      <!-- Botões de ação rápida -->
      <div style="display: flex; gap: 15px; margin-bottom: 30px;">
        <button class="btn btn-primary" onclick="window.location.href='/vendas'">💰 Ver Vendas</button>
        <button class="btn btn-secondary" onclick="window.location.href='/estoque'">📦 Gerenciar Estoque</button>
        <button class="btn btn-secondary" onclick="window.location.href='/pedidos'">📋 Criar Pedido</button>
        <button class="btn btn-secondary" onclick="sincronizarTudo()">🔄 Sincronizar Tudo</button>
      </div>

      <!-- Seção Principal com Gráfico -->
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">📈 Vendas dos Últimos 7 Dias</h3>
          <button class="btn btn-secondary">📊 Ver Relatório</button>
        </div>
        <div class="section-content">
          <div class="chart-container">
            <canvas id="vendasChart"></canvas>
          </div>
        </div>
      </div>

      <!-- Listas Resumidas -->
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
        <!-- Produtos -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">🧪 Top Produtos</h3>
            <a href="/produtos" class="btn btn-secondary">Ver todos</a>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 12px;">
              <% if (produtos && produtos.length > 0) { %>
                <% produtos.slice(0, 4).forEach(produto => { %>
                  <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                    <div>
                      <div style="font-weight: 600; color: #333; font-size: 14px;"><%= produto.nome %></div>
                      <div style="color: #666; font-size: 12px;">Estoque: <%= produto.estoque_atual %></div>
                    </div>
                    <div style="text-align: right;">
                      <div style="font-weight: bold; color: #018820;">R$ <%= parseFloat(produto.preco).toFixed(2) %></div>
                    </div>
                  </div>
                <% }) %>
              <% } else { %>
                <div style="text-align: center; color: #666; padding: 20px;">Nenhum produto</div>
              <% } %>
            </div>
          </div>
        </div>

        <!-- Vendas -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">💰 Vendas Recentes</h3>
            <a href="/vendas" class="btn btn-secondary">Ver todas</a>
          </div>
          <div class="section-content">
            <div style="display: grid; gap: 12px;">
              <% if (vendas && vendas.length > 0) { %>
                <% vendas.slice(0, 4).forEach(venda => { %>
                  <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                    <div>
                      <div style="font-weight: 600; color: #333; font-size: 14px;"><%= venda.produto_nome || 'Produto N/A' %></div>
                      <div style="color: #666; font-size: 12px;"><%= new Date(venda.data).toLocaleDateString('pt-BR') %></div>
                    </div>
                    <div style="text-align: right;">
                      <div style="font-weight: bold; color: #018820;">R$ <%= parseFloat(venda.valor_total || 0).toFixed(2) %></div>
                    </div>
                  </div>
                <% }) %>
              <% } else { %>
                <div style="text-align: center; color: #666; padding: 20px;">Nenhuma venda</div>
              <% } %>
            </div>
          </div>
        </div>
      </div>

    </main>

    <!-- Lateral direita com Tarefas, Calendário e Pergunte para IA -->
    <%- include('../components/sidebar-right') %>
  </div>

  <script>
    // Gráfico simples de vendas
    const vendasCtx = document.getElementById('vendasChart').getContext('2d');

    // Dados dos últimos 7 dias (simples)
    const labels = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
    const data = [12, 19, 8, 15, 22, 18, 25];

    new Chart(vendasCtx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Vendas',
          data: data,
          borderColor: '#018820',
          backgroundColor: 'rgba(1, 136, 32, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#018820',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f0f0f0'
            },
            ticks: {
              stepSize: 5
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });

    // Função para sincronizar tudo
    function sincronizarTudo() {
      alert('🔄 Sincronizando todas as plataformas...\n\n• Mercado Livre\n• Shopee\n• PCR Labor\n\nEsta operação pode levar alguns minutos.');

      // Simular sincronização
      setTimeout(() => {
        alert('✅ Sincronização concluída!\n\n• 5 novas vendas encontradas\n• 3 produtos atualizados\n• 2 pedidos processados');
        location.reload();
      }, 3000);
    }

    console.log('📊 Dashboard carregado e padronizado');
  </script>
</body>
</html>
