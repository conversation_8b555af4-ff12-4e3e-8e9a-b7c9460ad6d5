<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe-exact.css">
</head>
<body>
  <div class="app-layout">
    <%- include('../components/sidebar') %>
    
    <div class="main-content">
      <%- include('../components/topbar') %>
      
      <div class="content-area">
        <!-- Cards de Métricas -->
        <div class="cards-grid">
          <% if (plataformas && plataformas.length > 0) { %>
            <% plataformas.slice(0, 4).forEach((plataforma, index) => { %>
              <div class="metric-card">
                <div class="card-header">
                  <div class="card-icon">
                    <%= plataforma.nome === 'Mercado Livre' ? '🛒' : plataforma.nome === 'Shopee' ? '🛍️' : '🌐' %>
                  </div>
                  <h3 class="card-title"><%= plataforma.nome %></h3>
                </div>
                <div class="card-value">R$ <%= parseFloat(plataforma.valor_total || 0).toFixed(2) %></div>
                <div class="card-subtitle"><%= plataforma.total_vendas || 0 %> vendas</div>
              </div>
            <% }) %>
            
            <!-- Preencher cards restantes se necessário -->
            <% for (let i = plataformas.length; i < 4; i++) { %>
              <div class="metric-card">
                <div class="card-header">
                  <div class="card-icon">📊</div>
                  <h3 class="card-title">Plataforma <%= i + 1 %></h3>
                </div>
                <div class="card-value">R$ 0,00</div>
                <div class="card-subtitle">0 vendas</div>
              </div>
            <% } %>
          <% } else { %>
            <!-- Cards vazios se não houver plataformas -->
            <% for (let i = 0; i < 4; i++) { %>
              <div class="metric-card">
                <div class="card-header">
                  <div class="card-icon">📊</div>
                  <h3 class="card-title">Plataforma <%= i + 1 %></h3>
                </div>
                <div class="card-value">R$ 0,00</div>
                <div class="card-subtitle">0 vendas</div>
              </div>
            <% } %>
          <% } %>
        </div>
        
        <!-- Tabela de Performance das Plataformas -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">Performance por Plataforma</h3>
          </div>
          <table class="data-table">
            <thead>
              <tr>
                <th>Plataforma</th>
                <th>Total de Vendas</th>
                <th>Quantidade Vendida</th>
                <th>Faturamento</th>
                <th>Ticket Médio</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% if (plataformas && plataformas.length > 0) { %>
                <% plataformas.forEach(plataforma => { %>
                  <tr>
                    <td>
                      <%= plataforma.nome === 'Mercado Livre' ? '🛒' : plataforma.nome === 'Shopee' ? '🛍️' : '🌐' %>
                      <%= plataforma.nome %>
                    </td>
                    <td><%= plataforma.total_vendas || 0 %></td>
                    <td><%= plataforma.quantidade_total || 0 %> unidades</td>
                    <td>R$ <%= parseFloat(plataforma.valor_total || 0).toFixed(2) %></td>
                    <td>
                      R$ <%= plataforma.total_vendas > 0 ? (parseFloat(plataforma.valor_total || 0) / plataforma.total_vendas).toFixed(2) : '0.00' %>
                    </td>
                    <td>
                      <% if (parseFloat(plataforma.valor_total || 0) > 1000) { %>
                        <span style="color: #28a745;">Ativa</span>
                      <% } else if (parseFloat(plataforma.valor_total || 0) > 0) { %>
                        <span style="color: #ffc107;">Baixa</span>
                      <% } else { %>
                        <span style="color: #dc3545;">Inativa</span>
                      <% } %>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="6" style="text-align: center; color: #666; padding: 40px;">
                    Nenhuma plataforma encontrada
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
