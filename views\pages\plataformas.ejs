<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/dashboard.css">
</head>
<body>
  <%- include('../components/sidebar') %>
  
  <div class="main-content">
    <%- include('../components/topbar') %>
    
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>Plataformas de Venda</h1>
        <p>Gestão e análise de performance das plataformas</p>
      </div>
      
      <!-- Estatísticas gerais -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">🌐</div>
          <div class="stat-info">
            <h3><%= plataformas.length %></h3>
            <p>Plataformas Ativas</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-info">
            <h3>R$ <%= vendasPorPlataforma.reduce((total, p) => total + parseFloat(p.valor_total || 0), 0).toFixed(2) %></h3>
            <p>Faturamento Total</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <h3><%= vendasPorPlataforma.reduce((total, p) => total + parseInt(p.total_vendas || 0), 0) %></h3>
            <p>Total de Vendas</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🏆</div>
          <div class="stat-info">
            <% const melhorPlataforma = vendasPorPlataforma.reduce((melhor, atual) => 
              parseFloat(atual.valor_total || 0) > parseFloat(melhor.valor_total || 0) ? atual : melhor, 
              vendasPorPlataforma[0] || {}) %>
            <h3><%= melhorPlataforma.nome || 'N/A' %></h3>
            <p>Melhor Performance</p>
          </div>
        </div>
      </div>
      
      <!-- Ações -->
      <div class="action-bar">
        <button class="btn btn-primary" onclick="openAddPlatformModal()">
          ➕ Nova Plataforma
        </button>
        <div class="search-filters">
          <input type="text" placeholder="Buscar plataformas..." id="platformSearch">
          <select id="statusFilter">
            <option value="">Todos os status</option>
            <option value="active">Ativas</option>
            <option value="inactive">Inativas</option>
          </select>
          <button class="btn btn-secondary" onclick="exportPlatformReport()">
            📊 Exportar Relatório
          </button>
        </div>
      </div>
      
      <!-- Performance das plataformas -->
      <div class="dashboard-grid">
        <div class="dashboard-card">
          <h3>Performance por Plataforma</h3>
          <div class="platform-performance">
            <% vendasPorPlataforma.forEach(plataforma => { %>
              <div class="platform-card">
                <div class="platform-header">
                  <div class="platform-info">
                    <h4><%= plataforma.nome %></h4>
                    <span class="platform-status active">Ativa</span>
                  </div>
                  <div class="platform-icon">
                    <% if (plataforma.nome === 'Mercado Livre') { %>🛒
                    <% } else if (plataforma.nome === 'Shopee') { %>🛍️
                    <% } else { %>🌐<% } %>
                  </div>
                </div>
                
                <div class="platform-stats">
                  <div class="stat-item">
                    <span class="stat-label">Vendas</span>
                    <span class="stat-value"><%= plataforma.total_vendas || 0 %></span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Faturamento</span>
                    <span class="stat-value">R$ <%= parseFloat(plataforma.valor_total || 0).toFixed(2) %></span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Produtos</span>
                    <span class="stat-value"><%= plataforma.quantidade_total || 0 %></span>
                  </div>
                </div>
                
                <div class="platform-actions">
                  <button class="btn btn-sm btn-secondary" onclick="viewPlatformDetails(<%= plataforma.id_plataforma %>)">
                    Ver Detalhes
                  </button>
                  <button class="btn btn-sm btn-primary" onclick="editPlatform(<%= plataforma.id_plataforma %>)">
                    Editar
                  </button>
                </div>
              </div>
            <% }) %>
          </div>
        </div>
        
        <!-- Gráfico de comparação -->
        <div class="dashboard-card">
          <h3>Comparativo de Faturamento</h3>
          <div class="chart-container">
            <% const maxFaturamento = Math.max(...vendasPorPlataforma.map(p => parseFloat(p.valor_total || 0))) %>
            <% vendasPorPlataforma.forEach(plataforma => { %>
              <div class="chart-item">
                <span class="platform-name"><%= plataforma.nome %></span>
                <div class="chart-bar">
                  <div class="bar-fill" style="width: <%= maxFaturamento > 0 ? (parseFloat(plataforma.valor_total || 0) / maxFaturamento) * 100 : 0 %>%"></div>
                </div>
                <span class="platform-value">R$ <%= parseFloat(plataforma.valor_total || 0).toFixed(2) %></span>
              </div>
            <% }) %>
          </div>
        </div>
      </div>
      
      <!-- Lista de plataformas -->
      <div class="dashboard-card">
        <h3>Gerenciar Plataformas</h3>
        <div class="table-container">
          <table id="platformsTable">
            <thead>
              <tr>
                <th>Nome</th>
                <th>Total de Vendas</th>
                <th>Faturamento</th>
                <th>Produtos Vendidos</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% vendasPorPlataforma.forEach(plataforma => { %>
                <tr>
                  <td>
                    <div class="platform-name-cell">
                      <span class="platform-icon-small">
                        <% if (plataforma.nome === 'Mercado Livre') { %>🛒
                        <% } else if (plataforma.nome === 'Shopee') { %>🛍️
                        <% } else { %>🌐<% } %>
                      </span>
                      <%= plataforma.nome %>
                    </div>
                  </td>
                  <td><%= plataforma.total_vendas || 0 %></td>
                  <td>R$ <%= parseFloat(plataforma.valor_total || 0).toFixed(2) %></td>
                  <td><%= plataforma.quantidade_total || 0 %></td>
                  <td>
                    <span class="status-badge active">Ativa</span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-icon" onclick="viewPlatformDetails(<%= plataforma.id_plataforma %>)" title="Detalhes">
                        📊
                      </button>
                      <button class="btn-icon" onclick="editPlatform(<%= plataforma.id_plataforma %>)" title="Editar">
                        ✏️
                      </button>
                      <button class="btn-icon" onclick="togglePlatformStatus(<%= plataforma.id_plataforma %>)" title="Ativar/Desativar">
                        🔄
                      </button>
                    </div>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para nova plataforma -->
  <div id="platformModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="platformModalTitle">Nova Plataforma</h3>
        <span class="close" onclick="closePlatformModal()">&times;</span>
      </div>
      <div class="modal-body">
        <form id="platformForm">
          <div class="form-group">
            <label for="platformName">Nome da Plataforma:</label>
            <input type="text" id="platformName" name="nome" required>
          </div>
          
          <div class="form-group">
            <label for="platformUrl">URL (opcional):</label>
            <input type="url" id="platformUrl" name="url">
          </div>
          
          <div class="form-group">
            <label for="platformDescription">Descrição:</label>
            <textarea id="platformDescription" name="descricao" rows="3"></textarea>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="closePlatformModal()">
              Cancelar
            </button>
            <button type="submit" class="btn btn-primary">
              Salvar Plataforma
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <style>
    .platform-performance {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
    }
    
    .platform-card {
      background-color: #f8f9fa;
      border-radius: 10px;
      padding: 20px;
      border: 1px solid #e9ecef;
      transition: transform 0.3s ease;
    }
    
    .platform-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .platform-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .platform-info h4 {
      margin: 0;
      color: #333;
      font-size: 18px;
    }
    
    .platform-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
      background-color: #d4edda;
      color: #155724;
    }
    
    .platform-icon {
      font-size: 24px;
    }
    
    .platform-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px;
      margin-bottom: 15px;
    }
    
    .stat-item {
      text-align: center;
    }
    
    .stat-label {
      display: block;
      font-size: 12px;
      color: #666;
      margin-bottom: 5px;
    }
    
    .stat-value {
      display: block;
      font-size: 16px;
      font-weight: bold;
      color: #018820;
    }
    
    .platform-actions {
      display: flex;
      gap: 10px;
      justify-content: center;
    }
    
    .platform-name-cell {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .platform-icon-small {
      font-size: 16px;
    }
  </style>

  <script>
    function openAddPlatformModal() {
      document.getElementById('platformModalTitle').textContent = 'Nova Plataforma';
      document.getElementById('platformForm').reset();
      document.getElementById('platformModal').style.display = 'block';
    }
    
    function closePlatformModal() {
      document.getElementById('platformModal').style.display = 'none';
    }
    
    function editPlatform(id) {
      alert('Editar plataforma ID: ' + id);
    }
    
    function viewPlatformDetails(id) {
      alert('Ver detalhes da plataforma ID: ' + id);
    }
    
    function togglePlatformStatus(id) {
      if (confirm('Deseja alterar o status desta plataforma?')) {
        alert('Status alterado para plataforma ID: ' + id);
      }
    }
    
    function exportPlatformReport() {
      alert('Exportar relatório de plataformas');
    }
    
    // Fechar modal ao clicar fora
    window.onclick = function(event) {
      const modal = document.getElementById('platformModal');
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    }
    
    // Form submit
    document.getElementById('platformForm').addEventListener('submit', function(e) {
      e.preventDefault();
      alert('Plataforma salva com sucesso!');
      closePlatformModal();
    });
    
    // Filtros
    document.getElementById('platformSearch').addEventListener('input', function() {
      // Implementar busca
    });
    
    document.getElementById('statusFilter').addEventListener('change', function() {
      // Implementar filtro
    });
  </script>
</body>
</html>
