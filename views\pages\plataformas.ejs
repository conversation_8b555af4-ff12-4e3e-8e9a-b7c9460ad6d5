<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= pageTitle %></title>
  <link rel="stylesheet" href="/css/wireframe.css">
</head>
<body>
  <div class="app-container">
    <%- include('../components/sidebar') %>
    
    <div class="main-content">
      <%- include('../components/topbar') %>
      
      <div class="content-area">
        <div class="page-header">
          <h1 class="page-title">Plataformas</h1>
          <p class="page-subtitle">Performance e gestão de plataformas de venda</p>
        </div>
        
        <!-- Cards de Performance -->
        <div class="cards-grid">
          <% if (plataformas && plataformas.length > 0) { %>
            <% plataformas.forEach((plataforma, index) => { %>
              <div class="card">
                <div class="card-header">
                  <div class="card-icon" style="background-color: <%= ['#d4edda', '#d1ecf1', '#fff3cd'][index % 3] %>;">
                    <%= plataforma.nome === 'Mercado Livre' ? '🛒' : plataforma.nome === 'Shopee' ? '🛍️' : '🌐' %>
                  </div>
                  <div>
                    <h3 class="card-title"><%= plataforma.nome %></h3>
                  </div>
                </div>
                <div class="card-value" style="color: <%= ['#155724', '#0c5460', '#856404'][index % 3] %>;">
                  R$ <%= parseFloat(plataforma.valor_total || 0).toFixed(2) %>
                </div>
                <div class="card-trend">
                  <%= plataforma.total_vendas || 0 %> vendas • 
                  <%= plataforma.quantidade_total || 0 %> unidades
                </div>
              </div>
            <% }) %>
          <% } %>
          
          <!-- Card Total -->
          <div class="card">
            <div class="card-header">
              <div class="card-icon" style="background-color: #f8d7da;">📊</div>
              <div>
                <h3 class="card-title">Total Geral</h3>
              </div>
            </div>
            <div class="card-value" style="color: #721c24;">
              R$ <%= plataformas ? plataformas.reduce((total, p) => total + parseFloat(p.valor_total || 0), 0).toFixed(2) : '0.00' %>
            </div>
            <div class="card-trend">Todas as plataformas</div>
          </div>
        </div>
        
        <!-- Gráfico de Performance -->
        <div class="table-container">
          <div class="table-header">
            <h3 class="table-title">📊 Performance por Plataforma</h3>
          </div>
          <div style="padding: 30px;">
            <% if (plataformas && plataformas.length > 0) { %>
              <% 
                const maxValue = Math.max(...plataformas.map(p => parseFloat(p.valor_total || 0)), 1);
                plataformas.forEach((plataforma, index) => { 
                  const percentage = (parseFloat(plataforma.valor_total || 0) / maxValue) * 100;
                  const colors = ['#018820', '#3b82f6', '#f59e0b'];
              %>
                <div style="margin-bottom: 25px;">
                  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <span style="font-size: 20px;">
                        <%= plataforma.nome === 'Mercado Livre' ? '🛒' : plataforma.nome === 'Shopee' ? '🛍️' : '🌐' %>
                      </span>
                      <strong><%= plataforma.nome %></strong>
                    </div>
                    <div style="text-align: right;">
                      <div style="font-weight: bold; font-size: 18px;">R$ <%= parseFloat(plataforma.valor_total || 0).toFixed(2) %></div>
                      <div style="font-size: 12px; color: #666;"><%= plataforma.total_vendas || 0 %> vendas</div>
                    </div>
                  </div>
                  
                  <div style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden;">
                    <div style="
                      height: 100%; 
                      background: linear-gradient(90deg, <%= colors[index % 3] %>, <%= colors[index % 3] %>cc); 
                      width: <%= percentage %>%; 
                      border-radius: 10px;
                      transition: width 0.3s ease;
                    "></div>
                  </div>
                  
                  <div style="display: flex; justify-content: space-between; margin-top: 5px; font-size: 12px; color: #666;">
                    <span><%= plataforma.quantidade_total || 0 %> unidades vendidas</span>
                    <span><%= percentage.toFixed(1) %>% do total</span>
                  </div>
                </div>
              <% }) %>
            <% } else { %>
              <div style="text-align: center; color: #666; padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 16px;">🌐</div>
                <div style="font-size: 18px; margin-bottom: 8px;">Nenhuma plataforma encontrada</div>
                <div style="font-size: 14px; color: #999;">Configure as plataformas de venda para ver a performance</div>
              </div>
            <% } %>
          </div>
        </div>
        
        <!-- Comparativo Mensal -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
          <!-- Top Produtos por Plataforma -->
          <div class="table-container">
            <div class="table-header">
              <h3 class="table-title">🏆 Top Produtos por Plataforma</h3>
            </div>
            <div style="padding: 20px;">
              <% if (plataformas && plataformas.length > 0) { %>
                <% plataformas.forEach(plataforma => { %>
                  <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4 style="margin-bottom: 10px; color: #333;">
                      <%= plataforma.nome === 'Mercado Livre' ? '🛒' : plataforma.nome === 'Shopee' ? '🛍️' : '🌐' %>
                      <%= plataforma.nome %>
                    </h4>
                    <div style="font-size: 14px; color: #666;">
                      Produto mais vendido: <strong>Kit PCR COVID-19</strong><br>
                      Vendas: <strong><%= Math.floor(Math.random() * 20) + 5 %> unidades</strong><br>
                      Receita: <strong>R$ <%= (Math.random() * 1000 + 500).toFixed(2) %></strong>
                    </div>
                  </div>
                <% }) %>
              <% } else { %>
                <p style="text-align: center; color: #666;">Dados não disponíveis</p>
              <% } %>
            </div>
          </div>
          
          <!-- Estatísticas Detalhadas -->
          <div class="table-container">
            <div class="table-header">
              <h3 class="table-title">📈 Estatísticas Detalhadas</h3>
            </div>
            <div style="padding: 20px;">
              <div style="display: flex; flex-direction: column; gap: 15px;">
                <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                  <span>Taxa de Conversão Média:</span>
                  <strong>3.2%</strong>
                </div>
                
                <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                  <span>Tempo Médio de Venda:</span>
                  <strong>2.5 dias</strong>
                </div>
                
                <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                  <span>Margem de Lucro Média:</span>
                  <strong>35%</strong>
                </div>
                
                <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                  <span>Avaliação Média:</span>
                  <strong>4.8/5.0 ⭐</strong>
                </div>
                
                <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                  <span>Produtos Ativos:</span>
                  <strong><%= produtos ? produtos.length : 0 %></strong>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Ações -->
        <div style="margin-top: 30px; display: flex; gap: 15px;">
          <button class="btn btn-primary" onclick="syncPlatforms()">
            🔄 Sincronizar Plataformas
          </button>
          <button class="btn btn-secondary" onclick="exportPlatformReport()">
            📊 Exportar Relatório
          </button>
          <button class="btn btn-secondary" onclick="managePlatforms()">
            ⚙️ Gerenciar Plataformas
          </button>
        </div>
      </div>
    </div>
  </div>

  <script>
    function syncPlatforms() {
      alert('🔄 Sincronizando dados das plataformas...');
      // Implementar sincronização
    }
    
    function exportPlatformReport() {
      alert('📊 Gerando relatório de plataformas...');
      // Implementar exportação
    }
    
    function managePlatforms() {
      alert('⚙️ Abrindo configurações de plataformas...');
      // Implementar gerenciamento
    }
  </script>
</body>
</html>
