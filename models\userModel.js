const db = require('../config/db');
const bcrypt = require('bcrypt');

class Usuario {
  static async getAll() {
    try {
      const result = await db.query(`
        SELECT u.id_usuario, u.nome, u.email, u.created_at, u.updated_at,
               e.nome_fantasia as empresa_nome
        FROM Usuario u
        LEFT JOIN Empresa e ON u.id_empresa = e.id_empresa
        ORDER BY u.id_usuario
      `);
      return result.rows;
    } catch (error) {
      throw new Error(`Erro ao buscar usuários: ${error.message}`);
    }
  }

  static async getById(id) {
    try {
      const result = await db.query(`
        SELECT u.id_usuario, u.nome, u.email, u.created_at, u.updated_at,
               e.nome_fantasia as empresa_nome, e.id_empresa
        FROM Usuario u
        LEFT JOIN Empresa e ON u.id_empresa = e.id_empresa
        WHERE u.id_usuario = $1
      `, [id]);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Erro ao buscar usuário: ${error.message}`);
    }
  }

  static async getByEmail(email) {
    try {
      const result = await db.query('SELECT * FROM Usuario WHERE email = $1', [email]);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Erro ao buscar usuário por email: ${error.message}`);
    }
  }

  static async create(userData) {
    try {
      const { nome, email, senha, id_empresa } = userData;
      const senhaHash = await bcrypt.hash(senha, 10);

      const result = await db.query(
        'INSERT INTO Usuario (nome, email, senha_hash, id_empresa) VALUES ($1, $2, $3, $4) RETURNING id_usuario, nome, email, id_empresa, created_at',
        [nome, email, senhaHash, id_empresa]
      );
      return result.rows[0];
    } catch (error) {
      throw new Error(`Erro ao criar usuário: ${error.message}`);
    }
  }

  static async update(id, userData) {
    try {
      const { nome, email, id_empresa } = userData;
      const result = await db.query(
        'UPDATE Usuario SET nome = $1, email = $2, id_empresa = $3, updated_at = CURRENT_TIMESTAMP WHERE id_usuario = $4 RETURNING id_usuario, nome, email, id_empresa, updated_at',
        [nome, email, id_empresa, id]
      );
      return result.rows[0];
    } catch (error) {
      throw new Error(`Erro ao atualizar usuário: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      const result = await db.query('DELETE FROM Usuario WHERE id_usuario = $1 RETURNING id_usuario, nome, email', [id]);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Erro ao deletar usuário: ${error.message}`);
    }
  }

  static async validatePassword(email, senha) {
    try {
      const user = await this.getByEmail(email);
      if (!user) return false;

      return await bcrypt.compare(senha, user.senha_hash);
    } catch (error) {
      throw new Error(`Erro ao validar senha: ${error.message}`);
    }
  }
}

module.exports = Usuario;
