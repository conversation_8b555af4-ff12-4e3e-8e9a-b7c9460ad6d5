// routes/pageRoutes.js - Rotas para renderização de páginas

const express = require('express');
const router = express.Router();

// Importar controllers
const controladorDashboard = require('../controllers/controladorDashboard');
const controladorVendas = require('../controllers/controladorVendas');
const controladorProdutos = require('../controllers/controladorProdutos');
const controladorPedidos = require('../controllers/controladorPedidos');
const controladorUsuarios = require('../controllers/controladorUsuarios');

// Rota principal - redirecionar para dashboard
router.get('/', (req, res) => {
  res.redirect('/dashboard');
});

// Rotas de páginas principais
router.get('/dashboard', controladorDashboard.renderDashboard);
router.get('/vendas', controladorVendas.renderVendas);
router.get('/estoque', controladorProdutos.renderEstoque);
router.get('/produtos', controladorProdutos.renderProdutos);
router.get('/pedidos', controladorPedidos.renderPedidos);
router.get('/plataformas', controladorVendas.renderPlataformas);
router.get('/perfil', controladorUsuarios.renderPerfil);

// Rota de emails
router.get('/emails', (req, res) => {
  res.render('pages/emails', {
    pageTitle: 'Emails - PCR Labor',
    currentPage: 'emails'
  });
});

// Rota de login
router.get('/login', (req, res) => {
  res.render('pages/login', {
    pageTitle: 'Login - PCR Labor',
    currentPage: 'login'
  });
});

// Rota de cadastro
router.get('/cadastro', controladorUsuarios.renderCadastro);
router.post('/cadastro', controladorUsuarios.processarCadastro);

// Processar login
router.post('/login', (req, res) => {
  const { email, senha } = req.body;

  // Validação simples (em produção usar autenticação real)
  if (email === '<EMAIL>' && senha === 'admin123') {
    // Simular sessão (em produção usar express-session)
    res.redirect('/dashboard');
  } else {
    res.render('pages/login', {
      pageTitle: 'Login - PCR Labor',
      currentPage: 'login',
      error: 'Email ou senha incorretos'
    });
  }
});

// Rota de logout
router.get('/logout', (req, res) => {
  // Limpar sessão (em produção)
  res.redirect('/login');
});

module.exports = router;
