#!/usr/bin/env node

/**
 * Script para testar o login com usuários existentes
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testarLogin() {
  try {
    console.log('🔐 Testando sistema de login...\n');

    // Primeiro, vamos ver quais usuários existem
    console.log('📋 Buscando usuários existentes...');
    const usuariosResponse = await axios.get(`${BASE_URL}/api/usuarios`);
    const usuarios = usuariosResponse.data.data || usuariosResponse.data;
    
    console.log(`✅ Encontrados ${usuarios.length} usuários:\n`);
    usuarios.forEach((user, index) => {
      console.log(`${index + 1}. ${user.nome} (${user.email}) - Empresa: ${user.empresa_nome || 'N/A'}`);
    });

    // Testar login com usuários conhecidos
    const testesLogin = [
      {
        nome: 'Admin Demo',
        email: '<EMAIL>',
        senha: 'admin123'
      },
      {
        nome: 'Usuário Teste',
        email: '<EMAIL>',
        senha: 'usuario123'
      },
      {
        nome: 'Usuário Final',
        email: '<EMAIL>',
        senha: 'usuario123'
      }
    ];

    console.log('\n🧪 Testando login com diferentes usuários...\n');

    for (const teste of testesLogin) {
      console.log(`🔑 Testando login: ${teste.nome} (${teste.email})`);
      
      try {
        const response = await axios.post(`${BASE_URL}/login`, {
          email: teste.email,
          senha: teste.senha
        }, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          maxRedirects: 0,
          validateStatus: function (status) {
            return status >= 200 && status < 400;
          }
        });

        if (response.status === 302 || response.status === 301) {
          console.log(`✅ Login bem-sucedido! Redirecionado para: ${response.headers.location}`);
        } else {
          console.log(`✅ Login realizado (status: ${response.status})`);
        }

      } catch (error) {
        if (error.response && error.response.status === 302) {
          console.log(`✅ Login bem-sucedido! Redirecionado para: ${error.response.headers.location}`);
        } else {
          console.log(`❌ Falha no login: ${error.message}`);
          if (error.response && error.response.data) {
            const errorText = error.response.data.toString();
            if (errorText.includes('Email ou senha incorretos')) {
              console.log('   Motivo: Email ou senha incorretos');
            } else if (errorText.includes('não encontrado')) {
              console.log('   Motivo: Usuário não encontrado');
            }
          }
        }
      }
      
      console.log(''); // Linha em branco
    }

    // Testar login com dados inválidos
    console.log('🚫 Testando login com dados inválidos...\n');
    
    const testesInvalidos = [
      { email: '<EMAIL>', senha: '123456' },
      { email: '<EMAIL>', senha: 'senhaerrada' },
      { email: '', senha: '123456' },
      { email: '<EMAIL>', senha: '' }
    ];

    for (const teste of testesInvalidos) {
      console.log(`❌ Testando login inválido: ${teste.email || '(vazio)'} / ${teste.senha || '(vazio)'}`);
      
      try {
        await axios.post(`${BASE_URL}/login`, {
          email: teste.email,
          senha: teste.senha
        }, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });
        
        console.log('⚠️ Login deveria ter falhado, mas passou');
        
      } catch (error) {
        console.log('✅ Login falhou como esperado');
      }
      
      console.log('');
    }

    console.log('🎉 Teste de login concluído!');

  } catch (error) {
    console.error('💥 Erro no teste:', error.message);
  }
}

// Executar teste
if (require.main === module) {
  testarLogin();
}

module.exports = { testarLogin };
