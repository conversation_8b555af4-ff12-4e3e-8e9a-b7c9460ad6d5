<div class="topbar">
  <div class="topbar-left">
    <button class="menu-toggle" onclick="toggleSidebar()">☰</button>
    <div class="breadcrumb">
      <span>PCR Labor</span>
      <span class="separator">></span>
      <span class="current-page">
        <% if (currentPage === 'dashboard') { %>Dashboard<% } %>
        <% if (currentPage === 'vendas') { %>Vendas<% } %>
        <% if (currentPage === 'estoque') { %>Estoque<% } %>
        <% if (currentPage === 'produtos') { %>Produtos<% } %>
        <% if (currentPage === 'plataformas') { %>Plataformas<% } %>
      </span>
    </div>
  </div>
  
  <div class="topbar-right">
    <div class="search-box">
      <input type="text" placeholder="Buscar..." id="searchInput">
      <button type="button" onclick="performSearch()">🔍</button>
    </div>
    
    <div class="notifications">
      <button class="notification-btn" onclick="toggleNotifications()">
        🔔
        <span class="notification-badge">3</span>
      </button>
      <div class="notification-dropdown" id="notificationDropdown">
        <div class="notification-header">
          <h4>Notificações</h4>
        </div>
        <div class="notification-list">
          <div class="notification-item">
            <span class="notification-icon">⚠️</span>
            <div class="notification-content">
              <p>Estoque baixo: Kit PCR COVID-19</p>
              <small>2 horas atrás</small>
            </div>
          </div>
          <div class="notification-item">
            <span class="notification-icon">💰</span>
            <div class="notification-content">
              <p>Nova venda registrada</p>
              <small>4 horas atrás</small>
            </div>
          </div>
          <div class="notification-item">
            <span class="notification-icon">📦</span>
            <div class="notification-content">
              <p>Produto adicionado ao catálogo</p>
              <small>1 dia atrás</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="user-menu">
      <button class="user-btn" onclick="toggleUserMenu()">
        <span class="user-avatar">👤</span>
        <span class="user-name">Admin</span>
        <span class="dropdown-arrow">▼</span>
      </button>
      <div class="user-dropdown" id="userDropdown">
        <a href="#" class="dropdown-item">👤 Perfil</a>
        <a href="#" class="dropdown-item">⚙️ Configurações</a>
        <hr class="dropdown-divider">
        <a href="/login" class="dropdown-item">🚪 Sair</a>
      </div>
    </div>
  </div>
</div>

<script>
function toggleSidebar() {
  const sidebar = document.querySelector('.sidebar');
  sidebar.classList.toggle('collapsed');
}

function toggleNotifications() {
  const dropdown = document.getElementById('notificationDropdown');
  dropdown.classList.toggle('show');
}

function toggleUserMenu() {
  const dropdown = document.getElementById('userDropdown');
  dropdown.classList.toggle('show');
}

function performSearch() {
  const searchTerm = document.getElementById('searchInput').value;
  if (searchTerm.trim()) {
    // Implementar lógica de busca
    alert('Busca por: ' + searchTerm);
  }
}

// Fechar dropdowns ao clicar fora
document.addEventListener('click', function(event) {
  const notificationDropdown = document.getElementById('notificationDropdown');
  const userDropdown = document.getElementById('userDropdown');
  
  if (!event.target.closest('.notifications')) {
    notificationDropdown.classList.remove('show');
  }
  
  if (!event.target.closest('.user-menu')) {
    userDropdown.classList.remove('show');
  }
});
</script>
