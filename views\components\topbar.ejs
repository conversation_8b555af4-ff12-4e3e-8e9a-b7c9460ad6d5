<!-- TOPBAR - Seguindo Wireframe -->
<div class="topbar">
  <!-- <PERSON><PERSON> - Breadcrumb -->
  <div class="topbar-left">
    <div class="breadcrumb">
      <span>PCR Labor</span>
      <span> / </span>
      <span class="current">
        <% if (currentPage === 'dashboard') { %>Dashboard<% } %>
        <% if (currentPage === 'vendas') { %>Vendas<% } %>
        <% if (currentPage === 'estoque') { %>Estoque<% } %>
        <% if (currentPage === 'produtos') { %>Produtos<% } %>
        <% if (currentPage === 'plataformas') { %>Plataformas<% } %>
      </span>
    </div>
  </div>
  
  <!-- Lado Direito - Busca e Ações -->
  <div class="topbar-right">
    <!-- Caixa de Busca -->
    <div class="search-box">
      <input 
        type="text" 
        class="search-input" 
        placeholder="Buscar produtos, vendas..."
        id="globalSearch"
      >
      <button type="button" class="search-btn" onclick="performSearch()">
        🔍
      </button>
    </div>
    
    <!-- Notificações -->
    <div class="notifications">
      <button type="button" class="notification-btn" onclick="toggleNotifications()">
        🔔
        <span class="notification-badge" id="notificationCount">3</span>
      </button>
      
      <!-- Dropdown de Notificações -->
      <div class="notification-dropdown" id="notificationDropdown" style="display: none;">
        <div class="notification-header">
          <h4>Notificações</h4>
          <button onclick="markAllAsRead()">Marcar todas como lidas</button>
        </div>
        <div class="notification-list">
          <div class="notification-item unread">
            <div class="notification-icon">⚠️</div>
            <div class="notification-content">
              <div class="notification-title">Estoque Baixo</div>
              <div class="notification-text">Kit PCR Dengue com apenas 5 unidades</div>
              <div class="notification-time">2 min atrás</div>
            </div>
          </div>
          
          <div class="notification-item unread">
            <div class="notification-icon">💰</div>
            <div class="notification-content">
              <div class="notification-title">Nova Venda</div>
              <div class="notification-text">Venda de R$ 899,00 no Mercado Livre</div>
              <div class="notification-time">5 min atrás</div>
            </div>
          </div>
          
          <div class="notification-item unread">
            <div class="notification-icon">📊</div>
            <div class="notification-content">
              <div class="notification-title">Relatório Pronto</div>
              <div class="notification-text">Relatório mensal de vendas disponível</div>
              <div class="notification-time">10 min atrás</div>
            </div>
          </div>
        </div>
        <div class="notification-footer">
          <a href="/notifications">Ver todas as notificações</a>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* Estilos específicos do topbar */
.notifications {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  position: relative;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.notification-btn:hover {
  background-color: #f0f0f0;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 8px;
}

.notification-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.notification-header button {
  background: none;
  border: none;
  color: #018820;
  font-size: 12px;
  cursor: pointer;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f8ff;
}

.notification-icon {
  margin-right: 12px;
  font-size: 16px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.notification-text {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.notification-time {
  font-size: 11px;
  color: #999;
}

.notification-footer {
  padding: 12px 16px;
  text-align: center;
  border-top: 1px solid #e0e0e0;
}

.notification-footer a {
  color: #018820;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}
</style>

<script>
// Funcionalidades do topbar
function performSearch() {
  const searchTerm = document.getElementById('globalSearch').value;
  if (searchTerm.trim()) {
    // Implementar busca global
    console.log('Buscando por:', searchTerm);
    // Aqui você pode implementar a lógica de busca
  }
}

function toggleNotifications() {
  const dropdown = document.getElementById('notificationDropdown');
  dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
}

function markAllAsRead() {
  const unreadItems = document.querySelectorAll('.notification-item.unread');
  unreadItems.forEach(item => {
    item.classList.remove('unread');
  });
  
  // Atualizar contador
  document.getElementById('notificationCount').textContent = '0';
  document.getElementById('notificationCount').style.display = 'none';
}

// Fechar dropdown ao clicar fora
document.addEventListener('click', function(event) {
  const notifications = document.querySelector('.notifications');
  const dropdown = document.getElementById('notificationDropdown');
  
  if (!notifications.contains(event.target)) {
    dropdown.style.display = 'none';
  }
});

// Busca com Enter
document.getElementById('globalSearch').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    performSearch();
  }
});
</script>
