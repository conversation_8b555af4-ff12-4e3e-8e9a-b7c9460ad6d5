// routes/pageRoutes.js - Rotas para renderização de páginas

const express = require('express');
const router = express.Router();

// Importar controllers
const dashboardController = require('../controllers/dashboardController');
const vendaController = require('../controllers/vendaController');
const produtoController = require('../controllers/produtoController');
const pedidoController = require('../controllers/pedidoController');
const usuarioController = require('../controllers/usuarioController');

// Rota principal - redirecionar para dashboard
router.get('/', (req, res) => {
  res.redirect('/dashboard');
});

// Rotas de páginas principais
router.get('/dashboard', dashboardController.renderDashboard);
router.get('/vendas', vendaController.renderVendas);
router.get('/estoque', produtoController.renderEstoque);
router.get('/produtos', produtoController.renderProdutos);
router.get('/pedidos', pedidoController.renderPedidos);
router.get('/plataformas', vendaController.renderPlataformas);
router.get('/perfil', usuarioController.renderPerfil);

// Rota de emails
router.get('/emails', (req, res) => {
  res.render('pages/emails', {
    pageTitle: 'Emails - PCR Labor',
    currentPage: 'emails'
  });
});

// Rota de login
router.get('/login', (req, res) => {
  res.render('pages/login', {
    pageTitle: 'Login - PCR Labor',
    currentPage: 'login'
  });
});

// Processar login
router.post('/login', (req, res) => {
  const { email, senha } = req.body;

  // Validação simples (em produção usar autenticação real)
  if (email === '<EMAIL>' && senha === 'admin123') {
    // Simular sessão (em produção usar express-session)
    res.redirect('/dashboard');
  } else {
    res.render('pages/login', {
      pageTitle: 'Login - PCR Labor',
      currentPage: 'login',
      error: 'Email ou senha incorretos'
    });
  }
});

// Rota de logout
router.get('/logout', (req, res) => {
  // Limpar sessão (em produção)
  res.redirect('/login');
});

module.exports = router;
