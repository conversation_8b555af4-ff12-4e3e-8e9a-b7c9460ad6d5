// controllers/dashboardController.js

const Produto = require('../models/produtoModel');
const Venda = require('../models/vendaModel');
const Plataforma = require('../models/plataformaModel');

const renderDashboard = async (req, res) => {
  try {
    console.log('Carregando dashboard...'); // Debug

    // Dados de demonstração (caso o banco não esteja disponível)
    let produtos = [];
    let vendas = [];
    let plataformas = [];
    let vendasPorMes = [];

    try {
      // Tentar buscar dados do banco
      const [produtosDB, vendasDB, plataformasDB, vendasPorMesDB] = await Promise.all([
        Produto.getAll(),
        Venda.getAll(),
        Plataforma.getVendasPorPlataforma(),
        Venda.getVendasPorMes()
      ]);

      produtos = produtosDB;
      vendas = vendasDB;
      plataformas = plataformasDB;
      vendasPorMes = vendasPorMesDB;

      console.log('Dados carregados do banco com sucesso'); // Debug
    } catch (dbError) {
      console.log('Banco não disponível, usando dados de demonstração:', dbError.message); // Debug

      // Dados de demonstração
      produtos = [
        { id_produto: 1, nome: 'Kit PCR COVID-19', sku: 'PCR-COVID-001', preco: 89.90, estoque_atual: 150 },
        { id_produto: 2, nome: 'Kit PCR Influenza', sku: 'PCR-FLU-001', preco: 79.90, estoque_atual: 200 },
        { id_produto: 3, nome: 'Kit PCR Hepatite B', sku: 'PCR-HEP-001', preco: 95.50, estoque_atual: 100 }
      ];

      vendas = [
        { id_venda: 1, produto_nome: 'Kit PCR COVID-19', quantidade: 10, valor_total: 899.00, data: new Date('2025-01-15') },
        { id_venda: 2, produto_nome: 'Kit PCR Influenza', quantidade: 5, valor_total: 399.50, data: new Date('2025-01-14') }
      ];

      plataformas = [
        { nome: 'Mercado Livre', valor_total: 1500.00 },
        { nome: 'Shopee', valor_total: 800.00 },
        { nome: 'Site Próprio', valor_total: 600.00 }
      ];
    }

    // Calcular estatísticas
    const totalProdutos = produtos.length;
    const produtosEstoqueBaixo = produtos.filter(p => p.estoque_atual <= 10).length;

    const totalVendas = vendas.length;
    const vendasHoje = vendas.filter(v => {
      const hoje = new Date().toISOString().split('T')[0];
      const dataVenda = new Date(v.data).toISOString().split('T')[0];
      return dataVenda === hoje;
    }).length;

    const valorTotalVendas = vendas.reduce((total, venda) => total + parseFloat(venda.valor_total || 0), 0);

    console.log('Renderizando dashboard com dados:', { totalProdutos, totalVendas, valorTotalVendas }); // Debug

    res.render('pages/dashboard', {
      pageTitle: 'Dashboard - PCR Labor',
      currentPage: 'dashboard',
      stats: {
        totalProdutos,
        produtosEstoqueBaixo,
        totalVendas,
        vendasHoje,
        valorTotalVendas
      },
      produtos: produtos.slice(0, 5), // Últimos 5 produtos
      vendas: vendas.slice(0, 10), // Últimas 10 vendas
      plataformas,
      vendasPorMes
    });
  } catch (error) {
    console.error('Erro ao carregar dashboard:', error);
    res.status(500).render('pages/error', {
      pageTitle: 'Erro - PCR Labor',
      error: 'Erro ao carregar dashboard: ' + error.message
    });
  }
};

const renderLogin = (req, res) => {
  try {
    res.render('pages/login', {
      pageTitle: 'Login - PCR Labor',
      currentPage: 'login'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao carregar página de login'
    });
  }
};

const processLogin = async (req, res) => {
  try {
    const { email, senha } = req.body;

    console.log('Tentativa de login:', { email, senha }); // Debug

    // Validação simples para demonstração
    if (email === '<EMAIL>' && senha === 'admin123') {
      console.log('Login bem-sucedido, redirecionando para dashboard'); // Debug
      return res.redirect('/dashboard');
    } else {
      console.log('Credenciais inválidas'); // Debug
      return res.render('pages/login', {
        pageTitle: 'Login - PCR Labor',
        currentPage: 'login',
        error: 'Email ou senha inválidos'
      });
    }
  } catch (error) {
    console.error('Erro no processLogin:', error); // Debug
    return res.status(500).render('pages/login', {
      pageTitle: 'Login - PCR Labor',
      currentPage: 'login',
      error: 'Erro interno do servidor'
    });
  }
};

module.exports = {
  renderDashboard,
  renderLogin,
  processLogin
};
