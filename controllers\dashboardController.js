// controllers/dashboardController.js

const Produto = require('../models/produtoModel');
const Venda = require('../models/vendaModel');
const Plataforma = require('../models/plataformaModel');

const renderDashboard = async (req, res) => {
  try {
    // Buscar dados para o dashboard
    const [produtos, vendas, plataformas, vendasPorMes] = await Promise.all([
      Produto.getAll(),
      Venda.getAll(),
      Plataforma.getVendasPorPlataforma(),
      Venda.getVendasPorMes()
    ]);

    // Calcular estatísticas
    const totalProdutos = produtos.length;
    const produtosEstoqueBaixo = produtos.filter(p => p.estoque_atual <= 10).length;
    
    const totalVendas = vendas.length;
    const vendasHoje = vendas.filter(v => {
      const hoje = new Date().toISOString().split('T')[0];
      return v.data.toISOString().split('T')[0] === hoje;
    }).length;

    const valorTotalVendas = vendas.reduce((total, venda) => total + parseFloat(venda.valor_total || 0), 0);

    res.render('pages/dashboard', {
      pageTitle: 'Dashboard - PCR Labor',
      currentPage: 'dashboard',
      stats: {
        totalProdutos,
        produtosEstoqueBaixo,
        totalVendas,
        vendasHoje,
        valorTotalVendas
      },
      produtos: produtos.slice(0, 5), // Últimos 5 produtos
      vendas: vendas.slice(0, 10), // Últimas 10 vendas
      plataformas,
      vendasPorMes
    });
  } catch (error) {
    console.error('Erro ao carregar dashboard:', error);
    res.status(500).render('pages/error', {
      pageTitle: 'Erro - PCR Labor',
      error: 'Erro ao carregar dashboard'
    });
  }
};

const renderLogin = (req, res) => {
  try {
    res.render('pages/login', {
      pageTitle: 'Login - PCR Labor',
      currentPage: 'login'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao carregar página de login'
    });
  }
};

const processLogin = async (req, res) => {
  try {
    const { email, senha } = req.body;
    
    // Aqui você implementaria a lógica de autenticação
    // Por enquanto, vamos simular um login simples
    if (email === '<EMAIL>' && senha === 'admin123') {
      // Em uma implementação real, você criaria uma sessão ou JWT
      res.redirect('/dashboard');
    } else {
      res.render('pages/login', {
        pageTitle: 'Login - PCR Labor',
        currentPage: 'login',
        error: 'Email ou senha inválidos'
      });
    }
  } catch (error) {
    res.status(500).render('pages/login', {
      pageTitle: 'Login - PCR Labor',
      currentPage: 'login',
      error: 'Erro interno do servidor'
    });
  }
};

module.exports = {
  renderDashboard,
  renderLogin,
  processLogin
};
